package com.ruoyi.patrol.domain.request;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.enums.AuditStatusType;
import com.ruoyi.patrol.enums.InspectionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @Description:
 * @author: QD
 * @date: 2024年08月13日 17:12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssetBaseDataRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 巡查状态 true: 已巡查; false: 未巡查 */
    @ApiModelProperty(value = "巡查状态 true: 已巡查; false: 未巡查")
    private Boolean isCheck;

    @ApiModelProperty(value = "检查类型: 1, 桥梁日常巡查; 2, 桥梁经常检查; " +
            "3, 涵洞日常检查; 4, 涵洞经常检查; " +
            "5, 隧道日常巡查; 6, 隧道经常检查; " +
            "7, 隧道机电日常巡查; 8, 隧道机电经常检查;")
    @EnumValue
    private InspectionType type;


    @ApiModelProperty(value = "资产类型 1, 桥梁; 2, 涵洞; 3, 隧道;")
    @EnumValue
    private AssetType assetType;


    /** 经度 */
    @ApiModelProperty("经度")
    private Double longitude;

    /** 纬度 */
    @ApiModelProperty("纬度")
    private Double latitude;

    /** 检查时间 */
    @ApiModelProperty("检查时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkTime;

    /** 审核时间 */
    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

    /** 查询是否有巡查记录时间 */
    @ApiModelProperty("查询是否有巡查记录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkTimeHas;

    @ApiModelProperty("是否日常巡查")
    private Boolean isDailyCheck;


    @ApiModelProperty("资产名称")
    private String assetName;
    @ApiModelProperty("资产编码")
    private String assetCode;
    @ApiModelProperty("需要操作的deptId")
    private String deptId;
    @ApiModelProperty("资产id")
    private String assetId;
    @ApiModelProperty("资产ids")
    private List<String> ids;
    @ApiModelProperty("排除数据id")
    private List<String> excludeIds;
    @ApiModelProperty("养护路段Id")
    private String maintenanceSectionId;
    @ApiModelProperty("养护路段IdList")
    private List<String> maintenanceSectionIdList;
//    @ApiModelProperty("养护路段名称")
//    private String maintenanceSectionName;
    @ApiModelProperty("资产数据类型,多个")
    private List<String> assetDataTypes;
    @ApiModelProperty("路线id")
    private String routeId;
    @ApiModelProperty("路线名称")
    private String routeName;
    @ApiModelProperty("路线编码")
    private String routeCode;
    @ApiModelProperty("路线编码数组")
    private Set<String> routeCodes = new LinkedHashSet<>();
    @ApiModelProperty("管养单位id")
    private String managementMaintenanceId;
    @ApiModelProperty("管养单位name")
    private String managementMaintenanceName;
    @ApiModelProperty("管养单位id数组")
    private Set<String> managementMaintenanceIds = new LinkedHashSet<>();
    @ApiModelProperty("管养分处id")
    private String managementMaintenanceBranchId;
    @ApiModelProperty("管养分处name")
    private String managementMaintenanceBranchName;
    @ApiModelProperty("管养分处id数组")
    private Set<String> managementMaintenanceBranchIds = new LinkedHashSet<>();

    @ApiModelProperty("关键字查询")
    private String ks;

    @ApiModelProperty("删除标志")
    private Integer delFlag;

    @ApiModelProperty("状态")
    @EnumValue
    private AuditStatusType status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("检查开始时间")
    private LocalDateTime checkStartTime;

    @ApiModelProperty("阶段")
    private Integer stage;

    @ApiModelProperty("状态list")
    private List<Integer> statusList;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("检查结束时间")
    private LocalDateTime checkEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("到期时间")
    private LocalDateTime expiry;

    @ApiModelProperty("到期开始时间")
    private LocalDateTime expiryStartTime;

    @ApiModelProperty("到期结束时间")
    private LocalDateTime expiryEndTime;

    @ApiModelProperty("是否需要数据权限")
    private Boolean dataRule = true;

    @ApiModelProperty("权限用户id")
    private Long roleUserId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("自动生成巡查报告的时候")
    private List<LocalDate> nowDateList;

    @ApiModelProperty("巡查日志id")
    private String logId;

    @ApiModelProperty("导出checkid")
    private String checkId;

    @ApiModelProperty("导出checkIds")
    private Set<String> checkIds;

    @ApiModelProperty("自定义sql条件")
    private String customSqlCondition;

    @ApiModelProperty("是否有病害")
    private Boolean hasDisease;

    @ApiModelProperty("自定义排序")
    private String customSort;

    @ApiModelProperty("是否需要桩号排序")
    private Boolean stakeSort;

    @ApiModelProperty("导出文件名称")
    private String exportFileName;

    @ApiModelProperty("排序列")
    private String orderBy;

    @ApiModelProperty("排序方向")
    private String orderDirection;


    public AssetType getAssetType() {
        if(this.assetType != null){
            return this.assetType;
        }else{
            if(this.type != null){
                return this.type.getAssetType();
            }
        }
        return null;
    }

    public Boolean getIsDailyCheck() {
        if(this.isDailyCheck != null){
            return this.isDailyCheck;
        }else{
            if(this.type != null){
                return this.type.getFlag();
            }
        }
        return null;
    }

    public String getTableName() {
        AssetType assetType = this.getAssetType();
        if (assetType != null) {
            return assetType.getTableName();
        }
        return null;
    }

    /**
     * 处理资产基础数据请求中的 ID 列表和排除 ID 列表
     *
     * <p>处理流程:
     * 1. 校验入参有效性
     * 2. 从原始 ID 列表中移除需要排除的 ID
     * 3. 清空排除 ID 列表
     * 4. 更新处理后的 ID 列表
     *
     * <p>策略说明:
     * - 如果请求为空或 ID 列表为空，直接返回
     * - 仅当存在有效的排除 ID 列表时才进行处理
     * - 处理完成后会清空排除 ID 列表，避免重复处理
     *
     * <p>示例:
     * 原始 IDs: ["1", "2", "3", "4"]
     * 排除 IDs: ["2", "4"]
     * 处理后 IDs: ["1", "3"]
     */
    public void processIdAndExclude() {
        // 入参校验：请求为空或ID列表为空则直接返回
        if (this.getIds() == null || this.getIds().isEmpty()) {
            return;
        }

        List<String> ids = this.getIds();
        List<String> excludeIds = this.getExcludeIds();

        // 当存在需要排除的ID时进行处理
        if (excludeIds != null && !excludeIds.isEmpty()) {
            // 创建新列表避免直接修改原始列表
            ids = new ArrayList<>(ids);
            // 从ID列表中移除所有需要排除的ID
            ids.removeAll(excludeIds);
            // 清空排除ID列表，防止重复处理
            this.setExcludeIds(null);
        }

        // 更新处理后的ID列表
        this.setIds(ids);
    }

    public void setExcludeIds(List<String> excludeIds) {
        this.excludeIds = excludeIds;
        this.processIdAndExclude();
    }

    /**
     * 添加管养单位ID列表
     * <p>
     * 处理流程:
     * 1. 检查输入列表的有效性
     * 2. 初始化managementMaintenanceIds（如果为null）
     * 3. 将新的ID列表添加到现有集合中
     * <p>
     * 优化策略:
     * - 使用LinkedHashSet保证元素唯一性和插入顺序
     * - 使用Optional处理null值情况
     * <p>
     * 示例:
     * 原有ids: ["1", "2"]
     * 添加ids: ["2", "3", "4"]
     * 结果: ["1", "2", "3", "4"]
     *
     * @param ids 需要添加的管养单位ID列表
     * @return 当前对象（支持链式调用）
     */
    public AssetBaseDataRequest addManagementMaintenanceIds(List<String> ids) {
        // 检查输入列表是否为空
        if (CollectionUtil.isEmpty(ids)) {
            return this;
        }

        // 如果当前集合为null，初始化为LinkedHashSet
        if (this.managementMaintenanceIds == null) {
            this.managementMaintenanceIds = new LinkedHashSet<>();
        }

        // 添加所有新ID到现有集合中（LinkedHashSet自动去重）
        this.managementMaintenanceIds.addAll(ids);

        return this;
    }
    
    /**
     * 获取所有管养单位ID（包括管养单位和管养分处）的去重集合
     * <p>
     * 处理流程:
     * 1. 创建新的LinkedHashSet作为结果集
     * 2. 添加管养单位ID（如果存在）
     * 3. 添加管养分处ID（如果存在）
     * 4. 返回合并后的结果
     * <p>
     * 优化策略:
     * - 使用LinkedHashSet保证元素唯一性和插入顺序
     * - 使用Stream API进行集合操作
     * - 使用Optional处理null值情况
     * <p>
     * 示例:
     * managementMaintenanceIds: ["1", "2"]
     * managementMaintenanceBranchIds: ["2", "3", "4"]
     * 返回结果: ["1", "2", "3", "4"]
     *
     * @return 去重后的所有管养单位ID集合
     */
    public Set<String> getAllManagementIds() {
        // 创建新的LinkedHashSet用于存储结果
        Set<String> allIds = new LinkedHashSet<>();
        
        // 添加管养单位ID（如果存在）
        Optional.ofNullable(managementMaintenanceIds)
            .ifPresent(allIds::addAll);
        
        // 添加管养分处ID（如果存在）
        Optional.ofNullable(managementMaintenanceBranchIds)
            .ifPresent(allIds::addAll);
        
        return allIds;
    }

}
