<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patrol.mapper.PatrolInspectionLogsMapper">
    <resultMap type="com.ruoyi.patrol.domain.PatrolInspectionLogs" id="PatrolInspectionLogsResult">
        <result property="id" column="id"/>
        <result property="patrolType" column="patrol_type"/>
        <result property="maintenanceSectionId" column="maintenance_section_id"/>
        <result property="maintenanceSectionName" column="maintenance_section_name"/>
        <result property="routeCode" column="route_code"/>
        <result property="maintenanceUnitId" column="maintenance_unit_id"/>
        <result property="maintenanceUnitName" column="maintenance_unit_name"/>
        <result property="direction" column="direction"/>
        <result property="carNum" column="car_num"/>
        <result property="weather" column="weather"/>
        <result property="content" column="content"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="reportedTime" column="reported_time"/>
        <result property="collectTime" column="collect_time"/>
        <result property="status" column="status"/>
        <result property="patrolMileage" column="patrol_mileage"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="personStr" column="person_str"/>
        <result property="patrolUnitId" column="patrol_unit_id"/>
        <result property="patrolUnitName" column="patrol_unit_name"/>
    </resultMap>

    <sql id="base_column">
        id
        , patrol_type
             , maintenance_section_id
             , maintenance_section_name
             , route_code
             , maintenance_unit_id
             , maintenance_unit_name
             , direction
             , car_num
             , weather
             , content
             , start_time
             , end_time
             , reported_time
             , collect_time
             , status
             , patrol_mileage
             , person_str
             , patrol_unit_id
             , patrol_unit_name
    </sql>

    <sql id="base_batch_values">
        #{item.id},
        #{item.patrolType},
        #{item.maintenanceSectionId},
        #{item.maintenanceSectionName},
        #{item.routeCode},
        #{item.maintenanceUnitId},
        #{item.maintenanceUnitName},
        #{item.direction},
        #{item.carNum},
        #{item.weather},
        #{item.content},
        #{item.startTime},
        #{item.endTime},
        #{item.reportedTime},
        #{item.collectTime},
        #{item.status},
        #{item.patrolMileage},
        #{item.personStr},
        #{item.patrolUnitId},
        #{item.patrolUnitName}
    </sql>


    <sql id="where_column">
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="patrolType != null and patrolType != ''">
            AND patrol_type = #{patrolType}
        </if>
        <if test="maintenanceSectionId != null and maintenanceSectionId != ''">
            AND maintenance_section_id = #{maintenanceSectionId}
        </if>
        <if test="maintenanceSectionName != null and maintenanceSectionName != ''">
            AND maintenance_section_name = #{maintenanceSectionName}
        </if>
        <if test="routeCode != null and routeCode != ''">
            AND route_code like CONCAT('%',#{routeCode},'%')
        </if>
        <if test="maintenanceUnitId != null and maintenanceUnitId != ''">
            AND maintenance_unit_id = #{maintenanceUnitId}
        </if>
        <if test="maintenanceUnitName != null and maintenanceUnitName != ''">
            AND maintenance_unit_name = #{maintenanceUnitName}
        </if>
        <if test="patrolUnitId != null and patrolUnitId != ''">
            AND patrol_unit_id = #{patrolUnitId}
        </if>
        <if test="patrolUnitName != null and patrolUnitName != ''">
            AND patrol_unit_name = #{patrolUnitName}
        </if>
        <if test="direction != null and direction != ''">
            AND direction = #{direction}
        </if>
        <if test="carNum != null and carNum != ''">
            AND car_num = #{carNum}
        </if>
        <if test="carNumLike != null and carNumLike != ''">
            AND car_num like CONCAT('%',
            #{carNumLike}, '%')
        </if>
        <if test="weather != null and weather != ''">
            AND weather =
            #{weather}
        </if>
        <if test="content != null and content != ''">
            AND content = #{content}
        </if>
        <if test="startTime != null and startTime != ''">
            AND start_time = #{startTime}
        </if>
        <if test="startTimee != null and startTimee != ''">
            AND date_format(start_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(
            #{startTimee}, '%Y-%m-%d')
        </if>
        <if test="startTimes != null and startTimes != ''">
            AND date_format(start_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(
            #{startTimes}, '%Y-%m-%d')
        </if>
        <if test="endTime != null and endTime != ''">
            AND end_time =
            #{endTime}
        </if>
        <if test="reportedTime != null and reportedTime != ''">
            AND reported_time = #{reportedTime}
        </if>
        <if test="collectTime != null and collectTime != ''">
            AND collect_time = #{collectTime}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="collectTimee != null and collectTimee != ''">
            AND date_format(collect_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(
            #{collectTimee}, '%Y-%m-%d')
        </if>
        <if test="collectTimes != null and collectTimes != ''">
            AND date_format(collect_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(
            #{collectTimes}, '%Y-%m-%d')
        </if>
        <if test="patrolMileage != null and patrolMileage != ''">
            AND patrol_mileage =
            #{patrolMileage}
        </if>
        <if test="sectionIds != null and sectionIds.size() > 0">
            AND maintenance_section_id IN
            <foreach item="item" index="index" collection="sectionIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="preTime != null">
            AND DATE (collect_time) &gt;= DATE (#{preTime})
        </if>
        <if test="endTime != null">
            AND DATE (collect_time)
            &lt; DATE (#{endTime})
        </if>
        <if test="nowTime != null">
            AND DATE (collect_time) = DATE (now())
        </if>
    </sql>

    <sql id="set_column">
        <if test="patrolType != null">
            patrol_type = #{patrolType},
        </if>
        <if test="maintenanceSectionId != null">
            maintenance_section_id = #{maintenanceSectionId},
        </if>
        <if test="maintenanceSectionName != null">
            maintenance_section_name = #{maintenanceSectionName},
        </if>
        <if test="routeCode != null">
            route_code = #{routeCode},
        </if>
        <if test="maintenanceUnitId != null">
            maintenance_unit_id = #{maintenanceUnitId},
        </if>
        <if test="maintenanceUnitName != null">
            maintenance_unit_name = #{maintenanceUnitName},
        </if>
        <if test="patrolUnitId != null">
            patrol_unit_id = #{patrolUnitId},
        </if>
        <if test="patrolUnitName != null">
            patrol_unit_name = #{patrolUnitName},
        </if>
        <if test="direction != null">
            direction = #{direction},
        </if>
        <if test="carNum != null">
            car_num = #{carNum},
        </if>
        <if test="weather != null">
            weather = #{weather},
        </if>
        <if test="content != null">
            content = #{content},
        </if>
        <if test="startTime != null">
            start_time = #{startTime},
        </if>
        <if test="endTime != null">
            end_time = #{endTime},
        </if>
        <if test="reportedTime != null">
            reported_time = #{reportedTime},
        </if>
        <if test="collectTime != null">
            collect_time = #{collectTime},
        </if>
        <if test="status != null">
            status = #{status},
        </if>
        <if test="patrolMileage != null">
            patrol_mileage = #{patrolMileage},
        </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="PatrolInspectionLogsResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_inspection_logs
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="PatrolInspectionLogsResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_inspection_logs
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByUserAndTime" resultMap="PatrolInspectionLogsResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_inspection_logs
        <where>
            <if test="sectionIds != null and sectionIds.size() > 0">
                AND maintenance_section_id IN
                <foreach item="item" index="index" collection="sectionIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="preTime != null">
                AND DATE (collect_time) &gt;= DATE (#{preTime})
            </if>
            <if test="endTime != null">
                AND DATE (collect_time)
                &lt; DATE (#{endTime})
            </if>
            <if test="nowTime != null">
                AND DATE (collect_time) = DATE (now())
            </if>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByUser" resultMap="PatrolInspectionLogsResult">
        SELECT l.id,
        l.patrol_type,
        l.maintenance_section_id,
        l.maintenance_section_name,
        l.patrol_unit_id,
        l.patrol_unit_name,
        l.route_code,
        l.maintenance_unit_id,
        l.maintenance_unit_name,
        l.direction,
        l.car_num,
        l.weather,
        l.content,
        l.start_time,
        l.end_time,
        l.reported_time,
        l.collect_time,
        l.status,
        ROUND(IFNULL(l.patrol_mileage / 1000, 0), 3) as patrol_mileage,
        l.create_by,
        l.create_time,
        l.update_by,
        l.update_time,
        (SELECT GROUP_CONCAT(user_id) FROM patrol_inspection_user WHERE patrol_id = l.id GROUP BY patrol_id) AS userIdStr,
        IFNULL((SELECT GROUP_CONCAT(nick_name) FROM patrol_inspection_user WHERE patrol_id = l.id GROUP BY patrol_id), l.person_str) AS userNames,
        (SELECT GROUP_CONCAT(sign_id) FROM patrol_inspection_user WHERE patrol_id = l.id GROUP BY patrol_id) AS signIds
        FROM patrol_inspection_logs l
        LEFT JOIN ( SELECT pu.patrol_id, GROUP_CONCAT( pu.nick_name SEPARATOR ',' ) AS userNames FROM patrol_inspection_user pu GROUP BY pu.patrol_id ) AS u
        ON l.id = u.patrol_id
        <where>
            <include refid="where_column_l"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

    <!-- 通过参数查找用户巡查记录数量 -->
    <select id="countListByUser" resultType="java.lang.Integer">
        SELECT COUNT(l.id)
        FROM patrol_inspection_logs l
        LEFT JOIN ( SELECT pu.patrol_id, GROUP_CONCAT( pu.nick_name SEPARATOR ',' ) AS userNames FROM patrol_inspection_user pu GROUP BY pu.patrol_id ) AS u
        ON l.id = u.patrol_id
        <where>
            <include refid="where_column_l"/>
        </where>
    </select>

    <select id="selectPatrolInspectionLogsById" resultMap="PatrolInspectionLogsResult">
        SELECT p.id                       AS id,
               p.patrol_type              AS patrol_type,
               p.maintenance_section_id   AS maintenance_section_id,
               p.maintenance_section_name AS maintenance_section_name,
               p.route_code               AS route_code,
               p.maintenance_unit_id      AS maintenance_unit_id,
               p.maintenance_unit_name    AS maintenance_unit_name,
               p.direction                AS direction,
               p.car_num                  AS car_num,
               p.weather                  AS weather,
               p.content                  AS content,
               p.start_time               AS start_time,
               p.end_time                 AS end_time,
               p.reported_time            AS reported_time,
               p.collect_time             AS collect_time,
               p.status                   AS status,
               p.patrol_mileage           AS patrol_mileage,
               u.user_id_str              AS userIdStr
        FROM patrol_inspection_logs p
                 LEFT JOIN (SELECT pu.patrol_id,
                                   GROUP_CONCAT(pu.user_id SEPARATOR ',') AS user_id_str
                            FROM patrol_inspection_user pu
                                     JOIN
                                 patrol_inspection_logs pl
                                 ON
                                     pu.patrol_id = pl.id
                            GROUP BY pu.patrol_id) AS u
                           ON
                               p.id = u.patrol_id
        WHERE p.id = #{id}
    </select>

    <select id="selectLatestPatrolInspectionLogs" resultMap="PatrolInspectionLogsResult">
        SELECT p.id AS id,
        p.patrol_type AS patrol_type,
        p.maintenance_section_id AS maintenance_section_id,
        p.maintenance_section_name AS maintenance_section_name,
        p.route_code AS route_code,
        p.maintenance_unit_id AS maintenance_unit_id,
        p.maintenance_unit_name AS maintenance_unit_name,
        p.direction AS direction,
        p.car_num AS car_num,
        p.weather AS weather,
        p.content AS content,
        p.start_time AS start_time,
        p.end_time AS end_time,
        p.reported_time AS reported_time,
        p.collect_time AS collect_time,
        p.status AS status,
        p.patrol_mileage AS patrol_mileage,
        u.user_id_str AS userIdStr
        FROM
        patrol_inspection_logs p
        JOIN (
        SELECT maintenance_section_id,
        MAX(collect_time) AS latest_collect_time
        FROM patrol_inspection_logs
        WHERE 1 = 1
        <if test="lastTime != null">
            AND collect_time &gt;= #{lastTime}
        </if>
        <if test="nowTime != null">
            AND collect_time &lt; #{nowTime}
        </if>
        <if test="sectionIdList != null and sectionIdList.size() > 0">
            AND maintenance_section_id IN
            <foreach item="item" index="index" collection="sectionIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        maintenance_section_id
        ) AS latest
        ON
        p.maintenance_section_id = latest.maintenance_section_id
        AND p.collect_time = latest.latest_collect_time
        LEFT JOIN (
        SELECT
        pu.patrol_id,
        GROUP_CONCAT(pu.user_id SEPARATOR ',') AS user_id_str
        FROM
        patrol_inspection_user pu
        JOIN
        patrol_inspection_logs pl
        ON
        pu.patrol_id = pl.id
        GROUP BY
        pu.patrol_id
        ) AS u
        ON
        p.id = u.patrol_id;
    </select>

    <sql id="where_column_l">
        <if test="id != null and id != ''">
            AND l.id = #{id}
        </if>
        <if test="ids != null and ids.size() > 0">
            AND l.id IN
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="idList != null and idList.size() > 0">
            AND l.id IN
            <foreach collection="idList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="idListNot != null and idListNot.size() > 0">
            AND l.id NOT IN
            <foreach collection="idListNot" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="patrolType != null and patrolType != ''">
            AND l.patrol_type = #{patrolType}
        </if>
        <if test="patrolUnitId != null and patrolUnitId != ''">
            AND l.patrol_unit_id = #{patrolUnitId}
        </if>
        <if test="patrolUnitName != null and patrolUnitName != ''">
            AND l.patrol_unit_name = #{patrolUnitName}
        </if>
        <if test="maintenanceUnitId != null and maintenanceUnitId != ''">
            AND l.maintenance_unit_id = #{maintenanceUnitId}
        </if>
        <if test="maintenanceSectionId != null and maintenanceSectionId != ''">
            AND l.maintenance_section_id = #{maintenanceSectionId}
        </if>
        <if test="maintenanceSectionName != null and maintenanceSectionName != ''">
            AND l.maintenance_section_name like CONCAT('%',#{maintenanceSectionName},'%')
        </if>
        <if test="routeCode != null and routeCode != ''">
            AND l.route_code like CONCAT('%',#{routeCode},'%')
        </if>
        <if test="sectionIdList != null">
            AND l.maintenance_section_id IN
            <foreach item="item" index="index" collection="sectionIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="direction != null and direction != ''">
            AND l.direction = #{direction}
        </if>
        <if test="carNum != null and carNum != ''">
            AND l.car_num = #{carNum}
        </if>
        <if test="carNumLike != null and carNumLike != ''">
            AND l.car_num like CONCAT('%',
            #{carNumLike}, '%')
        </if>
        <if test="userNameLike != null and userNameLike != ''">
            AND u.userNames like CONCAT('%',
            #{userNameLike}, '%')
        </if>
        <if test="weather != null and weather != ''">
            AND l.weather =
            #{weather}
        </if>
        <if test="content != null and content != ''">
            AND l.content = #{content}
        </if>
        <if test="startTime != null and startTime != ''">
            AND l.start_time = #{startTime}
        </if>
        <if test="startTimee != null and startTimee != ''">
            AND date_format(l.start_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(
            #{startTimee}, '%Y-%m-%d')
        </if>
        <if test="startTimes != null and startTimes != ''">
            AND date_format(l.start_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(
            #{startTimes}, '%Y-%m-%d')
        </if>
        <if test="endTime != null and endTime != ''">
            AND l.end_time =
            #{endTime}
        </if>
        <if test="reportedTime != null and reportedTime != ''">
            AND l.reported_time = #{reportedTime}
        </if>
        <if test="collectTime != null and collectTime != ''">
            AND l.collect_time = #{collectTime}
        </if>
        <if test="status != null">
            AND l.status = #{status}
        </if>
        <if test="collectTimee != null and collectTimee != ''">
            AND date_format(l.collect_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(
            #{collectTimee}, '%Y-%m-%d')
        </if>
        <if test="collectTimes != null and collectTimes != ''">
            AND date_format(l.collect_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(
            #{collectTimes}, '%Y-%m-%d')
        </if>
        <if test="patrolMileage != null and patrolMileage != ''">
            AND l.patrol_mileage =
            #{patrolMileage}
        </if>
        <if test="createBy != null and createBy != ''">
            AND l.create_by = #{createBy}
        </if>
        <if test="createTime != null and createTime != ''">
            AND l.create_time = #{createTime}
        </if>
        <if test="updateBy != null and updateBy != ''">
            AND l.update_by = #{updateBy}
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND l.update_time = #{updateTime}
        </if>
    </sql>

    <!-- 定义公共的 WHERE 条件 -->
    <sql id="Common_Where_Clause">
        <where>
            <!-- 养护段条件 -->
            <if test="request.maintenanceSectionId != null and request.maintenanceSectionId != ''">
                AND maintenance_section_id = #{request.maintenanceSectionId}
            </if>
            <if test="request.maintenanceSectionIdList != null and request.maintenanceSectionIdList.size() > 0">
                AND maintenance_section_id IN
                <foreach collection="request.maintenanceSectionIdList" item="sectionId" open="(" separator=","
                         close=")">
                    #{sectionId}
                </foreach>
            </if>

            <!-- 管理养护条件 -->
            <if test="request.managementMaintenanceId != null and request.managementMaintenanceId != ''">
                AND maintain_unit_id = #{request.managementMaintenanceId}
            </if>
            <if test="request.managementMaintenanceIds != null and request.managementMaintenanceIds.size() > 0">
                AND maintain_unit_id IN
                <foreach collection="request.managementMaintenanceIds" item="maintenanceId" open="(" separator=","
                         close=")">
                    #{maintenanceId}
                </foreach>
            </if>

            <!-- ID条件 -->
            <if test="request.ids != null">
                <choose>
                    <when test="request.ids.size() > 0">
                        AND maintenance_section_id IN
                        <foreach collection="request.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1 = 0
                    </otherwise>
                </choose>
            </if>

            <!-- 排除ID条件 -->
            <if test="request.excludeIds != null and request.excludeIds.size() > 0">
                AND maintenance_section_id NOT IN
                <foreach collection="request.excludeIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <!-- 状态条件 -->
            <if test="request.status != null">
                AND status = #{request.status}
            </if>
            <if test="request.statusList != null and request.statusList.size() > 0">
                AND status IN
                <foreach collection="request.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>

            <!-- 检查时间 -->
            <if test="request.checkTime != null">
                <choose>
                    <when test="request.isDailyCheck == false">
                        AND DATE_FORMAT(collect_time, '%Y-%m') = DATE_FORMAT(#{request.checkTime}, '%Y-%m')
                    </when>
                    <otherwise>
                        AND DATE(collect_time) = DATE(#{request.checkTime})
                    </otherwise>
                </choose>
            </if>
            <!-- 检查时间条件: isDailyCheck为false时截取到月，其他情况截取到日 -->
            <if test="request.checkStartTime != null">
                AND collect_time &gt;=#{request.checkStartTime}
            </if>

            <if test="request.checkEndTime != null">
                AND collect_time &lt; #{request.checkEndTime}
            </if>

            <!-- 管养分处 -->
            <if test="request.managementMaintenanceBranchId != null and request.managementMaintenanceBranchId != ''">
                AND maintain_unit_id = #{request.managementMaintenanceBranchId}
            </if>
            <if test="request.managementMaintenanceBranchIds != null and request.managementMaintenanceBranchIds.size() > 0">
                AND maintain_unit_id IN
                <foreach collection="request.managementMaintenanceBranchIds" item="maintenanceBranchId" open="("
                         separator="," close=")">
                    #{maintenanceBranchId}
                </foreach>
            </if>
            <if test="request.managementMaintenanceBranchName != null and request.managementMaintenanceBranchName != ''">
                AND maintain_unit_name LIKE CONCAT('%', #{request.managementMaintenanceBranchName}, '%')
            </if>
        </where>
    </sql>

    <!-- 通过参数查找集合，获取patrol_mileage的和 -->
    <select id="getTotalPatrolMileage" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(patrol_mileage), 0)
        FROM patrol_inspection_logs
        <include refid="Common_Where_Clause"/>
    </select>

    <!-- 新增查询本月有数据的日子 -->
    <select id="findDaysWithDataBySectionAndMonth" resultType="java.lang.Integer">
        SELECT DISTINCT DAY (collect_time) AS day
        FROM patrol_inspection_logs
        WHERE maintenance_section_id = #{maintenanceSectionId}
          AND DATE_FORMAT(collect_time
            , '%Y-%m') = #{yearMonth}
        ORDER BY day ASC
    </select>

    <!-- 查找指定路段在指定时间之前最近的巡查记录 -->
    <select id="findLatestBySectionIdAndTime" resultMap="PatrolInspectionLogsResult">
        SELECT p.id                       AS id,
               p.patrol_type              AS patrol_type,
               p.maintenance_section_id   AS maintenance_section_id,
               p.maintenance_section_name AS maintenance_section_name,
                p.route_code               AS route_code,
               p.maintenance_unit_id      AS maintenance_unit_id,
               p.maintenance_unit_name    AS maintenance_unit_name,
               p.direction                AS direction,
               p.car_num                  AS car_num,
               p.weather                  AS weather,
               p.content                  AS content,
               p.start_time               AS start_time,
               p.end_time                 AS end_time,
               p.reported_time            AS reported_time,
               p.collect_time             AS collect_time,
               p.status                   AS status,
               p.patrol_mileage           AS patrol_mileage,
               u.user_id_str              AS userIdStr
        FROM patrol_inspection_logs p
                 LEFT JOIN (SELECT pu.patrol_id,
                                   GROUP_CONCAT(pu.user_id SEPARATOR ',') AS user_id_str
                            FROM patrol_inspection_user pu
                                     JOIN patrol_inspection_logs pl ON pu.patrol_id = pl.id
                            GROUP BY pu.patrol_id) AS u ON p.id = u.patrol_id
        WHERE p.maintenance_section_id = #{sectionId}
          AND DATE (p.collect_time) &lt;= DATE (#{time})
        ORDER BY p.collect_time DESC
            LIMIT 1
    </select>
    <select id="countByPatrolUnit" resultType="com.ruoyi.patrol.domain.vo.PatrolInspectionLogsTotalVO">
        SELECT
            maintenance_section_name as maintenanceSectionName,
            maintenance_unit_name as maintenanceSubUnitName,
            patrol_unit_name as patrolUnitName,
            start_time as startTime,
            COUNT( 1 ) count,
	SUM( patrol_mileage )  as patrolMileage,
	GROUP_CONCAT( id ) ids
        FROM
            (
            SELECT
            id,
            maintenance_section_name,
            maintenance_unit_name,
            patrol_mileage,
            patrol_unit_name,
            DATE_FORMAT( start_time, '%Y-%m-%d' ) start_time
            FROM
            patrol_inspection_logs


        <where>
            <if test="maintenanceSectionId != null and maintenanceSectionId != ''">
                AND maintenance_section_id = #{maintenanceSectionId}
            </if>
            <if test="maintenanceSectionName != null and maintenanceSectionName != ''">
                AND maintenance_section_name = #{maintenanceSectionName}
            </if>

            <if test="maintenanceUnitId != null and maintenanceUnitId != ''">
                AND maintenance_unit_id = #{maintenanceUnitId}
            </if>
            <if test="maintenanceUnitName != null and maintenanceUnitName != ''">
                AND maintenance_unit_name = #{maintenanceUnitName}
            </if>
            <if test="patrolUnitId != null and patrolUnitId != ''">
                AND patrol_unit_id = #{patrolUnitId}
            </if>
            <if test="patrolUnitName != null and patrolUnitName != ''">
                AND patrol_unit_name = #{patrolUnitName}
            </if>

            <if test="startTime != null and startTime != ''">
                AND date_format(start_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(
                #{startTime}, '%Y-%m-%d')
            </if>

            <if test="endTime != null and endTime != ''">
                AND date_format(start_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(
                #{endTime}, '%Y-%m-%d')
            </if>

            <if test="idList != null and idList.size() > 0">
                AND id IN
                <foreach collection="idList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            and maintenance_unit_name is not null
            and maintenance_section_name is not null

        </where>
            ORDER BY
            patrol_unit_name,
            start_time DESC

            ) patrol
        GROUP BY
            patrol.patrol_unit_name,
            patrol.start_time
        ORDER BY
            patrol_unit_name,
            start_time DESC

    </select>
    <select id="countByMaintenanceSubUnit" resultType="com.ruoyi.patrol.domain.vo.PatrolInspectionLogsTotalVO">
        SELECT
        maintenance_section_name as maintenanceSectionName,
        maintenance_unit_name as maintenanceSubUnitName,
--         patrol_unit_name as patrolUnitName,
        start_time as startTime,
        COUNT( 1 ) count,
        SUM( patrol_mileage )  as patrolMileage,
        GROUP_CONCAT( id ) ids
        FROM
        (
        SELECT
        id,
        maintenance_section_name,
        maintenance_unit_name,
        patrol_mileage,
        patrol_unit_name,
        DATE_FORMAT( start_time, '%Y-%m-%d' ) start_time
        FROM
        patrol_inspection_logs


        <where>
            <if test="maintenanceSectionId != null and maintenanceSectionId != ''">
                AND maintenance_section_id = #{maintenanceSectionId}
            </if>
            <if test="maintenanceSectionName != null and maintenanceSectionName != ''">
                AND maintenance_section_name = #{maintenanceSectionName}
            </if>

            <if test="maintenanceUnitId != null and maintenanceUnitId != ''">
                AND maintenance_unit_id = #{maintenanceUnitId}
            </if>
            <if test="maintenanceUnitName != null and maintenanceUnitName != ''">
                AND maintenance_unit_name = #{maintenanceUnitName}
            </if>
            <if test="patrolUnitId != null and patrolUnitId != ''">
                AND patrol_unit_id = #{patrolUnitId}
            </if>
            <if test="patrolUnitName != null and patrolUnitName != ''">
                AND patrol_unit_name = #{patrolUnitName}
            </if>

            <if test="startTime != null and startTime != ''">
                AND date_format(start_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(
                #{startTime}, '%Y-%m-%d')
            </if>

            <if test="endTime != null and endTime != ''">
                AND date_format(start_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(
                #{endTime}, '%Y-%m-%d')
            </if>

            <if test="idList != null and idList.size() > 0">
                AND id IN
                <foreach collection="idList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            and maintenance_unit_name is not null
            and maintenance_section_name is not null
            and maintenance_section_id is not null
            and start_time is not null

        </where>
        ORDER BY
        maintenance_unit_name,
        start_time DESC

        ) patrol
        GROUP BY

        patrol.maintenance_section_name,
        patrol.maintenance_unit_name,
        patrol.start_time
        ORDER BY
        maintenance_section_name,
        maintenance_unit_name,
        start_time DESC


    </select>

    <!-- 批量新增巡查日志 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO patrol_inspection_logs (<include refid="base_column"/>) VALUES
        <foreach collection="list" item="item" separator=",">
            (<include refid="base_batch_values"/>)
        </foreach>
    </insert>

    <!-- 批量删除巡查日志 -->
    <delete id="batchDelete" parameterType="java.util.List">
        DELETE FROM patrol_inspection_logs
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量更新里程数据 -->
    <update id="batchUpdateMileage" parameterType="java.util.List">
        UPDATE patrol_inspection_logs
        SET patrol_mileage = 
        CASE id
        <foreach collection="list" item="item">
            WHEN #{item.id} THEN #{item.patrolMileage}
        </foreach>
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <!-- 通过参数查找集合 (userNames 和 signIds 按顺序一一对应, NULL 的 sign_id 显示为 'NULL' 字符串) -->
    <select id="findListByUserWithOrderedSigns" resultMap="PatrolInspectionLogsResult">
        SELECT l.id,
        l.patrol_type,
        l.maintenance_section_id,
        l.maintenance_section_name,
        l.patrol_unit_id,
        l.patrol_unit_name,
        l.route_code,
        l.maintenance_unit_id,
        l.maintenance_unit_name,
        l.direction,
        l.car_num,
        l.weather,
        l.content,
        l.start_time,
        l.end_time,
        l.reported_time,
        l.collect_time,
        l.person_str,
        l.status,
        ROUND(IFNULL(l.patrol_mileage / 1000, 0), 3) as patrol_mileage,
        l.create_by,
        l.create_time,
        l.update_by,
        l.update_time,
        (SELECT GROUP_CONCAT(user_id ORDER BY user_id SEPARATOR ',') FROM patrol_inspection_user WHERE patrol_id = l.id GROUP BY patrol_id) AS userIdStr,
        (SELECT GROUP_CONCAT(nick_name ORDER BY user_id SEPARATOR ',') FROM patrol_inspection_user WHERE patrol_id = l.id GROUP BY patrol_id) AS userNames,
        (SELECT GROUP_CONCAT(IFNULL(sign_id, 'null') ORDER BY user_id SEPARATOR ',') FROM patrol_inspection_user WHERE patrol_id = l.id GROUP BY patrol_id) AS signIds
        FROM patrol_inspection_logs l
        LEFT JOIN ( SELECT pu.patrol_id, GROUP_CONCAT( pu.nick_name ORDER BY pu.user_id SEPARATOR ',' ) AS userNames FROM patrol_inspection_user pu GROUP BY pu.patrol_id ) AS u
        ON l.id = u.patrol_id
        <where>
            <include refid="where_column_l"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>
</mapper>