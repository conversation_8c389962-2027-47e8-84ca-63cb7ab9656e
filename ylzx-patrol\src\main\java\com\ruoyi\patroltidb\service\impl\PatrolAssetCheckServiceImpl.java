package com.ruoyi.patroltidb.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.context.SecurityContextHolder;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.manage.api.domain.BaseDataDomain;
import com.ruoyi.patrol.domain.*;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.domain.dto.BaseDataDomainWithDistance;
import com.ruoyi.patrol.domain.dto.PatrolDiseaseDTO;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.domain.request.BatchAuditRequest;
import com.ruoyi.patrol.domain.request.PatrolAssetCheckRequest;
import com.ruoyi.patrol.domain.vo.InspectionStatsVO;
import com.ruoyi.patrol.enums.*;
import com.ruoyi.patrol.mapper.PatrolAssetCheckMapper;
import com.ruoyi.patrol.processor.PatrolAssetCheckProcessor;
import com.ruoyi.patrol.service.*;
import com.ruoyi.patrol.utils.CacheToDomainUtils;
import com.ruoyi.patrol.utils.FrequencyUtils;
import com.ruoyi.patrol.utils.StackUtils;
import com.ruoyi.patroltidb.service.PatrolAssetCheckDetailService;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.system.api.RemoteDeptAuthService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteMaintenanceSectionService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.ImageTextRequest;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.dto.BaseMaintenanceSectionDTO;
import com.ruoyi.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 资产寻检查主表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@Service
@Slf4j
@Slave
public class PatrolAssetCheckServiceImpl extends ServiceImpl<PatrolAssetCheckMapper, PatrolAssetCheck>
        implements PatrolAssetCheckService {

    // 定义需要排除的缺陷类型集合
    private static final Set<String> EXCLUDED_DEFECTS = Set.of("未见异常", "无", "/", "\\", "未见", "正常", "未见/", "未见异常未见异常", "未见异/常", "正常保养", "：/", "//");

    @Resource
    private PatrolAssetCheckMapper patrolAssetCheckMapper;

    @Resource
    private PatrolInspectionLastService patrolInspectionLastService;

    @Resource
    private PatrolAssetCheckDetailService patrolAssetCheckDetailService;

    @Resource
    private PatrolInspectionLogsService patrolInspectionLogsService;

    @Resource
    private PatrolFrequencySettingsService patrolFrequencySettingsService;

    @Resource
    private BaseCacheService baseCacheService;

    @Resource
    private RemoteDeptAuthService remoteDeptAuthService;

    @Resource
    private RemoteUserService remoteUserService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RemoteFileService remoteFileService;

    @Resource
    private RemoteMaintenanceSectionService remoteMaintenanceSectionService;

    @Resource
    private RedisService redisService;

    @Resource
    PatrolPartsIgnoreService patrolPartsIgnoreService;

    @Resource
    private PatrolPartsInfoService patrolPartsInfoService;

    @Resource
    private PatrolAssetCheckProcessor patrolAssetCheckProcessor;

    // 定义一个特殊的对象来表示结束标志
    private static final PatrolAssetCheck END_MARKER = new PatrolAssetCheck();
    private static final List<PatrolAssetCheckDetail> END_MARKER_DETAIL = new ArrayList<>();


    private static final String PROCESS_KEY_PREFIX = "patrol:asset:process:";
    private static final String PROCESS_COMPLETE_SUFFIX = ":EOF";
    private static final long PROCESS_EXPIRE_TIME = 24; // 过期时间(小时)


    //region 补全数据
    @Override
    public void completeData() {
        final List<AssetType> assetTypes = Arrays.asList(AssetType.BRIDGE, AssetType.CULVERT, AssetType.TUNNEL);
        final YearMonth startYearMonth = YearMonth.of(2025, 1);
        final YearMonth endYearMonth = YearMonth.of(2025, 2);

        // 使用阻塞队列和CompletableFuture处理资产
        final BlockingQueue<PatrolAssetCheck> bridgeQueue = new LinkedBlockingQueue<>();
        final BlockingQueue<PatrolAssetCheck> culvertQueue = new LinkedBlockingQueue<>();
        final BlockingQueue<PatrolAssetCheck> tunnelQueue = new LinkedBlockingQueue<>();
        Map<AssetType, BlockingQueue<PatrolAssetCheck>> assetQueueMap = new HashMap<>();
        assetQueueMap.put(AssetType.BRIDGE, bridgeQueue);
        assetQueueMap.put(AssetType.CULVERT, culvertQueue);
        assetQueueMap.put(AssetType.TUNNEL, tunnelQueue);


        // 新建插入线程
        String processId = UUID.randomUUID().toString();
        Set<String> updateAssetIds = ConcurrentHashMap.newKeySet();
        CompletableFuture<Void> bridgeProcessing = CompletableFuture.runAsync(() -> processNonExistingAssets(
                bridgeQueue, updateAssetIds, AssetType.BRIDGE, false, processId));
        CompletableFuture<Void> culvertProcessing = CompletableFuture.runAsync(() -> processNonExistingAssets(
                culvertQueue, updateAssetIds, AssetType.CULVERT, false, processId));
        CompletableFuture<Void> tunnelProcessing = CompletableFuture.runAsync(() -> processNonExistingAssets(
                tunnelQueue, updateAssetIds, AssetType.TUNNEL, false, processId));

        // 并行获取各类资产的基础数据
        CompletableFuture<Map<String, List<BaseDataDomain>>> bridgeMapFuture = CompletableFuture.supplyAsync(() -> {
            AssetBaseDataRequest request = new AssetBaseDataRequest();
            request.setAssetType(AssetType.BRIDGE);
            request.setDataRule(false);
            List<BaseDataDomain> bridgeList = CacheToDomainUtils.convertList(baseCacheService.selectBaseDataResponseByAssetIds(request), AssetType.BRIDGE);
            return bridgeList.stream()
                    .filter(item -> item.getMaintenanceSectionId() != null)
                    .collect(Collectors.groupingBy(BaseDataDomain::getMaintenanceSectionId));
        });

        CompletableFuture<Map<String, List<BaseDataDomain>>> culvertMapFuture = CompletableFuture.supplyAsync(() -> {
            AssetBaseDataRequest request = new AssetBaseDataRequest();
            request.setAssetType(AssetType.CULVERT);
            request.setDataRule(false);
            List<BaseDataDomain> culvertList = CacheToDomainUtils.convertList(baseCacheService.selectBaseDataResponseByAssetIds(request), AssetType.CULVERT);
            return culvertList.stream()
                    .filter(item -> item.getMaintenanceSectionId() != null)
                    .collect(Collectors.groupingBy(BaseDataDomain::getMaintenanceSectionId));
        });

        CompletableFuture<Map<String, List<BaseDataDomain>>> tunnelMapFuture = CompletableFuture.supplyAsync(() -> {
            AssetBaseDataRequest request = new AssetBaseDataRequest();
            request.setAssetType(AssetType.TUNNEL);
            request.setDataRule(false);
            List<BaseDataDomain> tunnelList = CacheToDomainUtils.convertList(baseCacheService.selectBaseDataResponseByAssetIds(request), AssetType.TUNNEL);
            return tunnelList.stream()
                    .filter(item -> item.getMaintenanceSectionId() != null)
                    .collect(Collectors.groupingBy(BaseDataDomain::getMaintenanceSectionId));
        });


        // 等待所有异步任务完成并组装结果
        Map<AssetType, Map<String, List<BaseDataDomain>>> assetMap = new HashMap<>();
        assetMap.put(AssetType.BRIDGE, bridgeMapFuture.join());
        assetMap.put(AssetType.CULVERT, culvertMapFuture.join());
        assetMap.put(AssetType.TUNNEL, tunnelMapFuture.join());

        // 并行获取各类资产的巡查频率
        CompletableFuture<Map<String, Integer>> bridgeFrequencyFuture = CompletableFuture.supplyAsync(() ->
                patrolFrequencySettingsService.assetMapByType(AssetType.BRIDGE, false)
        );

        CompletableFuture<Map<String, Integer>> culvertFrequencyFuture = CompletableFuture.supplyAsync(() ->
                patrolFrequencySettingsService.assetMapByType(AssetType.CULVERT, false)
        );

        CompletableFuture<Map<String, Integer>> tunnelFrequencyFuture = CompletableFuture.supplyAsync(() ->
                patrolFrequencySettingsService.assetMapByType(AssetType.TUNNEL, false)
        );

        // 并行获取养护路线信息
        CompletableFuture<List<BaseMaintenanceSectionDTO>> maintenanceSectionFuture = CompletableFuture.supplyAsync(() -> {
            R<List<BaseMaintenanceSectionDTO>> maintenanceSectionR = remoteMaintenanceSectionService.findUserMaintenanceList(1L);
            if (maintenanceSectionR.getCode() != 200) {
                log.error("获取养护路线信息失败");
                throw new RuntimeException("获取养护路线信息失败: " + maintenanceSectionR.getMsg());
            }
            return maintenanceSectionR.getData();
        });

        // 等待所有异步任务完成并组装结果
        Map<AssetType, Map<String, Integer>> assetFrequencyMap = new HashMap<>();
        assetFrequencyMap.put(AssetType.BRIDGE, bridgeFrequencyFuture.join());
        assetFrequencyMap.put(AssetType.CULVERT, culvertFrequencyFuture.join());
        assetFrequencyMap.put(AssetType.TUNNEL, tunnelFrequencyFuture.join());

        // 获取养护路线信息结果
        List<BaseMaintenanceSectionDTO> maintenanceSectionList;
        try {
            maintenanceSectionList = maintenanceSectionFuture.join();
        } catch (CompletionException e) {
            log.error("获取养护路线信息失败", e);
            return;
        }
        // 并行处理养护路段
        maintenanceSectionList.parallelStream().forEach(baseMaintenanceSectionDTO -> {
            String maintenanceSectionId = baseMaintenanceSectionDTO.getMaintenanceSectionId();
            // 将 Date 转换为 LocalDateTime
            // 添加空值检查
            Date openingTimeDate = baseMaintenanceSectionDTO.getOpeningTime();
            if (openingTimeDate == null) {
                // 把startYearMonth转换为LocalDateTime
                openingTimeDate = Date.from(startYearMonth.atDay(31).atStartOfDay(ZoneId.systemDefault()).toInstant());
            }

            // 将 Date 转换为 LocalDateTime
            LocalDateTime openingDateTime = openingTimeDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();

            // 资产类型循环保持串行
            for (AssetType assetType : assetTypes) {
                Map<String, List<BaseDataDomain>> assetMapByType = assetMap.get(assetType);
                if (!assetMapByType.containsKey(maintenanceSectionId) ||
                        assetMapByType.get(maintenanceSectionId).isEmpty()) {
                    continue;
                }

                List<BaseDataDomain> assetList = assetMapByType.get(maintenanceSectionId);
                YearMonth openingTime = YearMonth.of(openingDateTime.getYear(), openingDateTime.getMonth().getValue());
                Map<String, Integer> fMap = assetFrequencyMap.get(assetType);
                InspectionType inspectionType = InspectionType.fromAssetType(assetType, false);

                // 查询现有数据并构建key集合
                AssetBaseDataRequest assetBaseDataRequest = new AssetBaseDataRequest();
                assetBaseDataRequest.setType(inspectionType);
                assetBaseDataRequest.setMaintenanceSectionId(maintenanceSectionId);
                List<PatrolAssetCheck> dataList = selectPatrolAssetCheck(assetBaseDataRequest, null, null,
                        new AtomicInteger());

                Set<String> keySet = dataList.stream()
                        .filter(check -> check.getCheckTime() != null)
                        .map(check -> check.getAssetId() + "+" +
                                DateTimeFormatter.ofPattern("yyyy-MM").format(check.getCheckTime().toInstant()
                                        .atZone(ZoneId.systemDefault())))
                        .collect(Collectors.toSet());

                // 资产循环保持串行
                for (BaseDataDomain baseDataDomain : assetList) {
                    Integer frequency = fMap.get(baseDataDomain.getId());
                    if (frequency == null) {
                        frequency = 1;
                    }

                    YearMonth currentTime = openingTime;

                    while (currentTime.isBefore(startYearMonth)) {
                        currentTime = currentTime.plusMonths(frequency);
                    }

                    while (currentTime.isBefore(endYearMonth)) {
                        String key = baseDataDomain.getAssetId() + "+" + currentTime.toString();
                        if (!keySet.contains(key)) {
                            PatrolAssetCheck patrolAssetCheck = new PatrolAssetCheck(baseDataDomain, frequency,
                                    currentTime, inspectionType);
                            patrolAssetCheck.setRemark("补全老数据." + patrolAssetCheck.getRemark());
                            assetQueueMap.get(assetType).add(patrolAssetCheck);
                        }
                        currentTime = currentTime.plusMonths(frequency);
                    }
                }
            }
        });
        // 等待处理线程完成
        bridgeQueue.add(END_MARKER);
        culvertQueue.add(END_MARKER);
        tunnelQueue.add(END_MARKER);
        bridgeProcessing.join();
        culvertProcessing.join();
        tunnelProcessing.join();
        if (updateAssetIds.isEmpty()) {
            log.info("无更新数据");
            return;
        }
    }

    /**
     * 补某月经常检查记录
     */
    @Override
    public void completeDataByMonth(YearMonth yearMonth, InspectionType type) {
        LocalDateTime yearMonthEnd = yearMonth.atEndOfMonth().atStartOfDay();
        AssetBaseDataRequest assetBaseDataRequest = new AssetBaseDataRequest();
        assetBaseDataRequest.setDataRule(false);
        assetBaseDataRequest.setType(type);
        assetBaseDataRequest.setIsCheck(false);
        assetBaseDataRequest.setCheckTimeHas(yearMonthEnd);
        List<BaseDataDomainWithDistance<BaseDataCache>> baseDataList = baseCacheService.listBy(assetBaseDataRequest, null, null, null);
        List<String> ids = baseDataList.stream().map(
                baseDate -> {
                    return baseDate.getBaseData().getAssetId();
                }
        ).collect(Collectors.toList());
        assetBaseDataRequest = new AssetBaseDataRequest();
        assetBaseDataRequest.setType(type);
        assetBaseDataRequest.setDataRule(false);
        assetBaseDataRequest.setIds(ids);
        List<?> assetResponseList = baseCacheService.selectBaseDataResponseByAssetIds(assetBaseDataRequest);
        List<BaseDataDomain> baseDataDomainList = new ArrayList<>();
        for (Object o : assetResponseList) {
            BaseDataDomain baseDataDomain = new BaseDataDomain();
            BeanUtils.copyProperties(o, baseDataDomain);
            baseDataDomainList.add(baseDataDomain);
        }
        Map<String, Integer> frequencyMap = patrolFrequencySettingsService.assetMapByType(type.getAssetType(), false);

        // 创建一个列表来存储生成的PatrolAssetCheck对象
        List<PatrolAssetCheck> patrolAssetCheckList = new ArrayList<>();

        for (BaseDataDomain baseDataDomain : baseDataDomainList) {
            Integer frequency = FrequencyUtils.getFrequency(frequencyMap.get(baseDataDomain.getAssetId()), type.getAssetType());
            PatrolAssetCheck patrolAssetCheck = new PatrolAssetCheck(baseDataDomain, frequency, yearMonth, type);
            patrolAssetCheck.setCheckTime(Date.from(yearMonthEnd.atZone(ZoneId.systemDefault()).toInstant()));
            this.setExpiryAndFrequency(patrolAssetCheck);
            patrolAssetCheck.setCheckTime(null);
            LocalDateTime expiry = patrolAssetCheck.getExpiry();
            // 如果不是yearMonth当月，不能前不能后，正正好的
            if (!YearMonth.from(expiry.toLocalDate()).equals(yearMonth)) {
                continue;
            }
            // 将生成的对象添加到列表中
            patrolAssetCheckList.add(patrolAssetCheck);

            // 每满100个就保存一次
            if (patrolAssetCheckList.size() >= 100) {
                savePatrolAssetCheck(patrolAssetCheckList, type.getAssetType().getTableName(), false);
                log.info("批量插入{}条经常检查记录", patrolAssetCheckList.size());
                patrolAssetCheckList.clear();
            }
        }

        // 保存剩余的记录
        if (!patrolAssetCheckList.isEmpty()) {
            savePatrolAssetCheck(patrolAssetCheckList, type.getAssetType().getTableName(), false);
            log.info("批量插入剩余{}条经常检查记录", patrolAssetCheckList.size());
        }
    }

    /**
     * 老数据补充主表的病害数量
     */

    //
    // 添加停止标记的DTO对象
    private static final PatrolDiseaseDTO END_MARKER_DISEASE = new PatrolDiseaseDTO();

    @Override
    public void completeDiseaseNum() {
        log.info("开始执行病害数量补全任务");

        // 使用阻塞队列和CompletableFuture处理资产
        final BlockingQueue<PatrolDiseaseDTO> bridgeQueue = new LinkedBlockingQueue<>();
        final BlockingQueue<PatrolDiseaseDTO> culvertQueue = new LinkedBlockingQueue<>();
        final BlockingQueue<PatrolDiseaseDTO> tunnelQueue = new LinkedBlockingQueue<>();
        Map<AssetType, BlockingQueue<PatrolDiseaseDTO>> assetQueueMap = new HashMap<>();
        assetQueueMap.put(AssetType.BRIDGE, bridgeQueue);
        assetQueueMap.put(AssetType.CULVERT, culvertQueue);
        assetQueueMap.put(AssetType.TUNNEL, tunnelQueue);
        log.info("初始化队列完成");


        // Set<String> updateAssetIds = ConcurrentHashMap.newKeySet();
        // CompletableFuture<Void> bridgeProcessing = CompletableFuture.runAsync(() -> updateDiseaseNumbers(
        //         bridgeQueue, updateAssetIds, AssetType.BRIDGE));
        // CompletableFuture<Void> culvertProcessing = CompletableFuture.runAsync(() -> updateDiseaseNumbers(
        //         culvertQueue, updateAssetIds, AssetType.CULVERT));
        // CompletableFuture<Void> tunnelProcessing = CompletableFuture.runAsync(() -> updateDiseaseNumbers(
        //         tunnelQueue, updateAssetIds, AssetType.TUNNEL));
        log.info("启动处理线程完成");

        log.info("开始获取资产ID列表");
        CompletableFuture<List<String>> bridgeIdsFuture = CompletableFuture.supplyAsync(() ->
                {
                    log.info("开始获取桥梁资产ID列表");
                    AssetBaseDataRequest request = new AssetBaseDataRequest();
                    request.setAssetType(AssetType.BRIDGE);
                    request.setDataRule(false);
                    List<String> ids = baseCacheService.getBaseDataResponseId(request);
                    log.info("获取到{}个桥梁资产ID", ids.size());
                    return ids;
                }
        );
        CompletableFuture<List<String>> culvertIdsFuture = CompletableFuture.supplyAsync(() ->
                {
                    log.info("开始获取涵洞资产ID列表");
                    AssetBaseDataRequest request = new AssetBaseDataRequest();
                    request.setAssetType(AssetType.CULVERT);
                    request.setDataRule(false);
                    List<String> ids = baseCacheService.getBaseDataResponseId(request);
                    log.info("获取到{}个涵洞资产ID", ids.size());
                    return ids;
                }
        );
        CompletableFuture<List<String>> tunnelIdsFuture = CompletableFuture.supplyAsync(() ->
                {
                    log.info("开始获取隧道资产ID列表");
                    AssetBaseDataRequest request = new AssetBaseDataRequest();
                    request.setAssetType(AssetType.TUNNEL);
                    request.setDataRule(false);
                    List<String> ids = baseCacheService.getBaseDataResponseId(request);
                    log.info("获取到{}个隧道资产ID", ids.size());
                    return ids;
                }
        );

        // 等待所有异步任务完成并组装结果
        log.info("等待资产ID列表获取完成");
        List<String> bridgeIds = bridgeIdsFuture.join();
        List<String> culvertIds = culvertIdsFuture.join();
        List<String> tunnelIds = tunnelIdsFuture.join();
        log.info("所有资产ID列表获取完成");

        // 开三个进程分别处理桥梁、涵洞、隧道
        CompletableFuture<Void> bridgeProcess = CompletableFuture.runAsync(() -> {
            log.info("开始并行处理{}个桥梁资产", bridgeIds.size());
            processAssetDisease(bridgeIds, AssetType.BRIDGE, assetQueueMap.get(AssetType.BRIDGE));
            log.info("桥梁资产病害数据处理完成");
        });

        log.info("开始处理涵洞资产病害数据");
        CompletableFuture<Void> culvertProcess = CompletableFuture.runAsync(() -> {
            log.info("开始并行处理{}个涵洞资产", culvertIds.size());
            processAssetDisease(culvertIds, AssetType.CULVERT, assetQueueMap.get(AssetType.CULVERT));
            log.info("涵洞资产病害数据处理完成");
        });

        log.info("开始处理隧道资产病害数据");
        CompletableFuture<Void> tunnelProcess = CompletableFuture.runAsync(() -> {
            log.info("开始并行处理{}个隧道资产", tunnelIds.size());
            processAssetDisease(tunnelIds, AssetType.TUNNEL, assetQueueMap.get(AssetType.TUNNEL));
            log.info("隧道资产病害数据处理完成");
        });

        // 等待所有处理完成
        log.info("等待所有资产处理完成");
        CompletableFuture.allOf(bridgeProcess, culvertProcess, tunnelProcess).join();
        log.info("所有资产处理完成，开始添加结束标记");

        // 在等待所有处理完成之前，添加结束标记
        CompletableFuture.allOf(bridgeProcess, culvertProcess, tunnelProcess)
                .thenRun(() -> {
                    // 添加结束标记到每个队列
                    bridgeQueue.add(END_MARKER_DISEASE);
                    culvertQueue.add(END_MARKER_DISEASE);
                    tunnelQueue.add(END_MARKER_DISEASE);
                    log.info("结束标记添加完成");
                })
                .join();

        log.info("病害数量补全任务执行完成");
    }

    public void completeBridgeDiseaseNum() {
        log.info("开始执行桥梁病害数量补全任务");

        final BlockingQueue<PatrolDiseaseDTO> bridgeQueue = new LinkedBlockingQueue<>();
        Set<String> updateAssetIds = ConcurrentHashMap.newKeySet(); // Still using ConcurrentHashMap for thread-safety if internal methods use parallelism

        log.info("开始获取桥梁资产ID列表");
        AssetBaseDataRequest request = new AssetBaseDataRequest();
        request.setAssetType(AssetType.BRIDGE);
        request.setDataRule(false);
        List<String> bridgeIds = baseCacheService.getBaseDataResponseId(request);
        log.info("获取到{}个桥梁资产ID", bridgeIds.size());

        if (bridgeIds.isEmpty()) {
            log.info("没有桥梁资产需要处理，任务结束。");
            return;
        }

        log.info("开始处理桥梁资产病害数据，共{}个资产", bridgeIds.size());
        processAssetDisease(bridgeIds, AssetType.BRIDGE, bridgeQueue);
        log.info("桥梁资产病害数据处理完成");

        // Add end marker to the queue to signal the end of data for updateDiseaseNumbers
        bridgeQueue.add(END_MARKER_DISEASE);
        log.info("结束标记已添加到队列");

        log.info("开始更新数据库中的病害数量");
        updateDiseaseNumbers(bridgeQueue, updateAssetIds, AssetType.BRIDGE);
        log.info("数据库病害数量更新完成");

        log.info("桥梁病害数量补全任务执行完成");
    }

    private void processAssetDisease(List<String> assetIds, AssetType assetType,
                                     BlockingQueue<PatrolDiseaseDTO> queue) {
        int total = assetIds.size();
        AtomicInteger current = new AtomicInteger(0);
        long startTime = System.currentTimeMillis();

        assetIds.parallelStream().forEach(assetId -> {
            try {
                AssetBaseDataRequest request = new AssetBaseDataRequest();
                request.setAssetType(assetType);
                request.setAssetId(assetId);
                request.setDataRule(false);
                // 2025-01-10 00:00:00
                request.setCheckEndTime(LocalDateTime.of(2025, 1, 10, 0, 0, 0));
                List<PatrolAssetCheck> patrolAssetChecks = this.selectPatrolAssetCheck(request, null, null, null);

                // 循环每次取100条数据
                for (int i = 0; i < patrolAssetChecks.size(); i += 100) {
                    List<PatrolAssetCheck> subList = patrolAssetChecks.subList(i, Math.min(i + 100, patrolAssetChecks.size()));
                    List<String> subListIds = subList.stream().map(PatrolAssetCheck::getId).collect(Collectors.toList());
                    List<PatrolDiseaseDTO> patrolDiseaseDTOS =
                            patrolAssetCheckDetailService.diseaseNumber(patrolAssetCheckDetailService.selectByCheckId(subListIds, assetType.getTableName()));
                    queue.addAll(patrolDiseaseDTOS);
                }

                // 更新进度并打印日志
                int progress = current.incrementAndGet();
                if (progress % 100 == 0 || progress == total) { // 每处理100个资产或处理完成时打印进度
                    String assetTypeName = switch (assetType) {
                        case BRIDGE -> "桥梁";
                        case CULVERT -> "涵洞";
                        case TUNNEL -> "隧道";
                        default -> "";
                    };

                    // 计算已用时间（秒）
                    long elapsedSeconds = (System.currentTimeMillis() - startTime) / 1000;
                    String elapsedTime = String.format("%d分%d秒",
                            elapsedSeconds / 60,
                            elapsedSeconds % 60);

                    // 计算预估剩余时间（秒）
                    String estimatedTime = "0分0秒";
                    if (progress > 0) {
                        double avgTimePerItem = (double) elapsedSeconds / progress;
                        long estimatedSeconds = Math.round(avgTimePerItem * (total - progress));
                        estimatedTime = String.format("%d分%d秒",
                                estimatedSeconds / 60,
                                estimatedSeconds % 60);
                    }

                    log.info("{}生成进度: {}/{}，完成度: {}%，已用时: {}，预计剩余时间: {}",
                            assetTypeName,
                            progress,
                            total,
                            String.format("%.2f", (progress * 100.0 / total)),
                            elapsedTime,
                            estimatedTime);
                }
            } catch (Exception e) {
                log.error("处理{}[{}]病害数据时发生错误", assetType.name(), assetId, e);
            }
        });
    }

    private void updateDiseaseNumbers(BlockingQueue<PatrolDiseaseDTO> patrolDiseaseDTOS,
                                      Set<String> updateAssetIds,
                                      AssetType assetType) {
        String tableName = assetType.getTableName();
        List<PatrolDiseaseDTO> batch = new ArrayList<>();

        while (true) {
            try {
                PatrolDiseaseDTO dto = patrolDiseaseDTOS.poll(10, TimeUnit.MILLISECONDS);
                if (dto == null) {
                    continue;
                }

                // 如果收到结束标记，处理剩余的批次后退出
                if (dto == END_MARKER_DISEASE) {
                    if (!batch.isEmpty()) {
                        // 执行最后一批更新
                        log.info("批量更新{}条{}病害数据", batch.size(), assetType.name());
                        updateDiseaseNumbers(batch, tableName);
                        // 添加到已更新集合
                        batch.forEach(item -> updateAssetIds.add(item.getId()));
                    }
                    break;
                }

                // 添加到批次
                batch.add(dto);

                // 当批次达到100条时执行更新
                if (batch.size() >= 100) {
                    log.info("批量更新{}条{}病害数据", batch.size(), assetType.name());
                    updateDiseaseNumbers(batch, tableName);
                    // 添加到已更新集合
                    batch.forEach(item -> updateAssetIds.add(item.getId()));
                    // 清空批次
                    batch.clear();
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("处理病害数据被中断", e);
                break;
            } catch (Exception e) {
                log.error("处理病害数据发生错误", e);
                // 发生错误时也尝试处理当前批次
                if (!batch.isEmpty()) {
                    try {
                        updateDiseaseNumbers(batch, tableName);
                        batch.forEach(item -> updateAssetIds.add(item.getId()));
                    } catch (Exception ex) {
                        log.error("处理剩余批次时发生错误", ex);
                    }
                }
                break;
            }
        }
    }

    /**
     * 批量更新病害数量
     */
    private void updateDiseaseNumbers(List<PatrolDiseaseDTO> batch, String tableName) {
        if (batch == null || batch.isEmpty()) {
            return;
        }

        try {
            // 切换到从库
            DynamicDataSourceContextHolder.push("slave");
            // 执行批量更新
            baseMapper.updateDiseaseNumbers(batch, tableName);
        } finally {
            // 清理数据源上下文
            DynamicDataSourceContextHolder.clear();
        }
    }

    // 重计算老数据的到期时间
//    @Override
    public void recalculateExpiry() {
        log.info("重新计算老数据的到期时间");

        // 使用阻塞队列和CompletableFuture处理资产
//        final BlockingQueue<PatrolDiseaseDTO> bridgeQueue = new LinkedBlockingQueue<>();
//        final BlockingQueue<PatrolDiseaseDTO> culvertQueue = new LinkedBlockingQueue<>();
//        final BlockingQueue<PatrolDiseaseDTO> tunnelQueue = new LinkedBlockingQueue<>();
//        Map<AssetType, BlockingQueue<PatrolDiseaseDTO>> assetQueueMap = new HashMap<>();
//        assetQueueMap.put(AssetType.BRIDGE, bridgeQueue);
//        assetQueueMap.put(AssetType.CULVERT, culvertQueue);
//        assetQueueMap.put(AssetType.TUNNEL, tunnelQueue);
//        log.info("初始化队列完成");
//
//
//        Set<String> updateAssetIds = ConcurrentHashMap.newKeySet();
//        CompletableFuture<Void> bridgeProcessing = CompletableFuture.runAsync(() -> updateExpiry(
//                bridgeQueue, updateAssetIds, AssetType.BRIDGE));
//        CompletableFuture<Void> culvertProcessing = CompletableFuture.runAsync(() -> updateExpiry(
//                culvertQueue, updateAssetIds, AssetType.CULVERT));
//        CompletableFuture<Void> tunnelProcessing = CompletableFuture.runAsync(() -> updateExpiry(
//                tunnelQueue, updateAssetIds, AssetType.TUNNEL));
//        log.info("启动处理线程完成");

//        log.info("开始获取资产ID列表");
//        CompletableFuture<List<String>> bridgeIdsFuture = CompletableFuture.supplyAsync(() ->
//                {
//                    log.info("开始获取桥梁资产ID列表");
//                    AssetBaseDataRequest request = new AssetBaseDataRequest();
//                    request.setAssetType(AssetType.BRIDGE);
//                    request.setDataRule(false);
//                    List<String> ids = baseCacheService.getBaseDataResponseId(request);
//                    log.info("获取到{}个桥梁资产ID", ids.size());
//                    return ids;
//                }
//        );
//        CompletableFuture<List<String>> culvertIdsFuture = CompletableFuture.supplyAsync(() ->
//                {
//                    log.info("开始获取涵洞资产ID列表");
//                    AssetBaseDataRequest request = new AssetBaseDataRequest();
//                    request.setAssetType(AssetType.CULVERT);
//                    request.setDataRule(false);
//                    List<String> ids = baseCacheService.getBaseDataResponseId(request);
//                    log.info("获取到{}个涵洞资产ID", ids.size());
//                    return ids;
//                }
//        );
//        CompletableFuture<List<String>> tunnelIdsFuture = CompletableFuture.supplyAsync(() ->
//                {
//                    log.info("开始获取隧道资产ID列表");
//                    AssetBaseDataRequest request = new AssetBaseDataRequest();
//                    request.setAssetType(AssetType.TUNNEL);
//                    request.setDataRule(false);
//                    List<String> ids = baseCacheService.getBaseDataResponseId(request);
//                    log.info("获取到{}个隧道资产ID", ids.size());
//                    return ids;
//                }
//        );
//
//        // 等待所有异步任务完成并组装结果
//        log.info("等待资产ID列表获取完成");
//        List<String> bridgeIds = bridgeIdsFuture.join();
//        List<String> culvertIds = culvertIdsFuture.join();
//        List<String> tunnelIds = tunnelIdsFuture.join();
//        log.info("所有资产ID列表获取完成");
        AssetBaseDataRequest requestBridge = new AssetBaseDataRequest();
        requestBridge.setAssetType(AssetType.BRIDGE);
        // 设置2025-03-01 00:00:00为到期时间
        requestBridge.setDataRule(false);
        List<String> bridgeIds = baseCacheService.getBaseDataResponseId(requestBridge);
        log.info("到期时间处理:开始并行处理{}个桥梁资产", bridgeIds.size());
        processAssetExpiry(bridgeIds, AssetType.BRIDGE);
        log.info("到期时间处理:桥梁资产到期时间数据处理完成");
        AssetBaseDataRequest request = new AssetBaseDataRequest();
        request.setAssetType(AssetType.CULVERT);
        request.setDataRule(false);
        List<String> culvertIds = baseCacheService.getBaseDataResponseId(request);
        log.info("到期时间处理:开始并行处理{}个涵洞资产", culvertIds.size());
        processAssetExpiry(culvertIds, AssetType.CULVERT);
        log.info("到期时间处理:涵洞资产到期时间数据处理完成");
        AssetBaseDataRequest requestTunnel = new AssetBaseDataRequest();
        requestTunnel.setAssetType(AssetType.TUNNEL);
        requestTunnel.setDataRule(false);
        List<String> tunnelIds = baseCacheService.getBaseDataResponseId(requestTunnel);
        log.info("到期时间处理:开始并行处理{}个隧道资产", tunnelIds.size());
        processAssetExpiry(tunnelIds, AssetType.TUNNEL);
        log.info("到期时间处理:隧道资产到期时间数据处理完成");
//        culvertQueue.add(END_MARKER_DISEASE);
//        log.info("开始并行处理{}个隧道资产", tunnelIds.size());
//        processAssetExpiry(tunnelIds, AssetType.TUNNEL, assetQueueMap.get(AssetType.TUNNEL));
//        log.info("隧道资产到期时间数据处理完成");

//        // 开三个进程分别处理桥梁、涵洞、隧道
//        log.info("开始处理桥梁资产到期时间数据");
//        CompletableFuture<Void> bridgeProcess = CompletableFuture.runAsync(() -> {
//            log.info("开始并行处理{}个桥梁资产", bridgeIds.size());
//            processAssetExpiry(bridgeIds, AssetType.BRIDGE, assetQueueMap.get(AssetType.BRIDGE));
//            log.info("桥梁资产到期时间数据处理完成");
//        });
//
//        log.info("开始处理涵洞资产到期时间数据");
//        CompletableFuture<Void> culvertProcess = CompletableFuture.runAsync(() -> {
//            log.info("开始并行处理{}个涵洞资产", culvertIds.size());
//            processAssetExpiry(culvertIds, AssetType.CULVERT, assetQueueMap.get(AssetType.CULVERT));
//            log.info("涵洞资产到期时间数据处理完成");
//        });
//
//        log.info("开始处理隧道资产到期时间数据");
//        CompletableFuture<Void> tunnelProcess = CompletableFuture.runAsync(() -> {
//            log.info("开始并行处理{}个隧道资产", tunnelIds.size());
//            processAssetExpiry(tunnelIds, AssetType.TUNNEL, assetQueueMap.get(AssetType.TUNNEL));
//            log.info("隧道资产到期时间数据处理完成");
//        });
//
//        // 等待所有处理完成
//        log.info("等待所有资产处理完成");
//        CompletableFuture.allOf(bridgeProcess, culvertProcess, tunnelProcess).join();
//        log.info("所有资产处理完成，开始添加结束标记");
//
//        // 在等待所有处理完成之前，添加结束标记
//        CompletableFuture.allOf(bridgeProcess, culvertProcess, tunnelProcess)
//                .thenRun(() -> {
//                    // 添加结束标记到每个队列
//                    bridgeQueue.add(END_MARKER_DISEASE);
//                    culvertQueue.add(END_MARKER_DISEASE);
//                    tunnelQueue.add(END_MARKER_DISEASE);
//                    log.info("结束标记添加完成");
//                })
//                .join();
//
//        log.info("病害数量补全任务执行完成");
    }

    private void processAssetExpiry(List<String> assetIds, AssetType assetType) {
        int total = assetIds.size();
        int current = 0;
        List<PatrolFrequencySettings> frequencySettings = patrolFrequencySettingsService.listByType(assetType);
        Map<String, PatrolFrequencySettings> frequencySettingsMap = frequencySettings.stream()
                .collect(Collectors.toMap(PatrolFrequencySettings::getAssetId,
                        settings -> settings,
                        (existing, replacement) -> existing));

        for (String assetId : assetIds) {
            current++;
            PatrolFrequencySettings frequency = frequencySettingsMap.get(assetId);
            try {
                AssetBaseDataRequest request = new AssetBaseDataRequest();
                request.setAssetType(assetType);
                request.setAssetId(assetId);
                request.setDataRule(false);
                request.setExpiryStartTime(LocalDateTime.of(2025, 3, 4, 0, 0));
                List<PatrolAssetCheck> patrolAssetChecks = this.selectPatrolAssetCheck(request, null, null, null);
                if (patrolAssetChecks.isEmpty()) {
                    continue;
                }
                log.info("到期时间处理:{}-{}搜索完成检查记录：{}", assetType.getDescription(), assetId, patrolAssetChecks.size());
                // 过滤掉checkTime为空的记录，并按照checkTime排序升序
                InspectionType inspectionTypeRegular = InspectionType.fromAssetType(assetType, false);
                InspectionType inspectionTypeDaily = InspectionType.fromAssetType(assetType, true);

                // 分别获取日常检查和经常检查的记录并排序
                List<PatrolAssetCheck> regularChecks = patrolAssetChecks.stream()
                        .filter(check -> inspectionTypeRegular.equals(check.getType()))
                        .peek(check -> {
                            if (check.getCheckTime() == null && check.getExpiry() != null) {
                                check.setCheckTime(
                                        Date.from(check.getExpiry().atZone(ZoneId.systemDefault()).toInstant()));
                            }
                        })
                        .filter(check -> check.getCheckTime() != null)
                        .sorted(Comparator.comparing(PatrolAssetCheck::getCheckTime))
                        .collect(Collectors.toList());

                List<PatrolAssetCheck> dailyChecks = patrolAssetChecks.stream()
                        .filter(check -> inspectionTypeDaily.equals(check.getType()))
                        .peek(check -> {
                            if (check.getCheckTime() == null && check.getExpiry() != null) {
                                check.setCheckTime(
                                        Date.from(check.getExpiry().atZone(ZoneId.systemDefault()).toInstant()));
                            }
                        })
                        .filter(check -> check.getCheckTime() != null)
                        .sorted(Comparator.comparing(PatrolAssetCheck::getCheckTime))
                        .collect(Collectors.toList());
                // 初始化频率设置
                if (frequency == null) {
                    frequency = initializeFrequencySettings(assetType);
                } else {
                    ensureFrequencySettings(frequency, assetType);
                }

                // 分别处理两种类型的检查记录
                log.info("到期时间处理:开始处理经常{}[{}]的到期时间(数量:{})", assetType.getDescription(), assetId, regularChecks.size());
                processChecks(regularChecks, frequency.getMonthFrequency(), assetType);
                log.info("到期时间处理:开始处理日常{}[{}]的到期时间(数量:{})", assetType.getDescription(), assetId, dailyChecks.size());
                processChecks(dailyChecks, frequency.getDayFrequency(), assetType);

                log.info("目前进度: {}/{}", current, total);

            } catch (Exception e) {
                log.error("处理{}[{}]到期时间时发生错误", assetType.name(), assetId, e);
            }

        }
    }

    private void processChecks(List<PatrolAssetCheck> checks, int frequency, AssetType assetType) {
        String tableName = assetType.getTableName();
        if (checks == null || checks.isEmpty()) {
            return;
        }
        List<PatrolDiseaseDTO> batch = new ArrayList<>();
        PatrolAssetCheck pre = null;
        int nextIndex = findNextValidCheck(checks, 0);

        while (nextIndex < checks.size()) {
            PatrolAssetCheck next = checks.get(nextIndex);
            LocalDateTime expiry = null;

            if (pre == null) {
                // 处理第一条记录
                expiry = calculateExpiry(null,
                        next.getCheckTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        next.getType(),
                        frequency,
                        next);
            } else {
                // 使用前一条记录的信息创建基础检查对象
                PatrolCheckBase patrolCheckBase = new PatrolCheckBase();
                patrolCheckBase.setExpiry(pre.getExpiry());
                patrolCheckBase.setFrequency(frequency);

                // 为当前记录计算到期时间
                PatrolCheckBase[] patrolCheckBases = new PatrolCheckBase[1];
                patrolCheckBases[0] = patrolCheckBase;
                expiry = calculateExpiry(patrolCheckBases,
                        next.getCheckTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        next.getType(),
                        frequency,
                        next);
            }

            if (expiry != null) {
                next.setExpiry(expiry);
                PatrolDiseaseDTO expiryDTO = new PatrolDiseaseDTO(next.getId(), next.getExpiry());
                batch.add(expiryDTO);

                // 当batch达到100条时执行更新
                if (batch.size() >= 100) {
                    this.updateExpiryBatch(batch, tableName);
                    log.info("到期时间处理:{}:批量更新{}条{}到期时间数据完成", assetType.getDescription(), batch.size(), tableName);
                    batch.clear();
                }
            }

            // 移动指针
            pre = next;
            nextIndex = findNextValidCheck(checks, nextIndex + 1);
        }

        // 处理剩余的批次
        if (!batch.isEmpty()) {
            this.updateExpiryBatch(batch, tableName);
            log.info("到期时间处理:{}:批量更新{}条{}到期时间数据完成", assetType.getDescription(), batch.size(), tableName);
        }
//        log.info("到期时间处理:{}:处理{}条{}到期时间数据完成", assetType.getDescription(),checks.size(), tableName);
    }

    /**
     * 初始化频率设置
     */
    private PatrolFrequencySettings initializeFrequencySettings(AssetType assetType) {
        PatrolFrequencySettings frequency = new PatrolFrequencySettings();
        frequency.setDayFrequency(assetType == AssetType.CULVERT ? 3 : 1);
        frequency.setMonthFrequency(assetType == AssetType.CULVERT ? 3 : 1);
        return frequency;
    }

    /**
     * 确保频率设置有效
     */
    private void ensureFrequencySettings(PatrolFrequencySettings frequency, AssetType assetType) {
        if (frequency.getDayFrequency() == null || frequency.getDayFrequency() == 0) {
            frequency.setDayFrequency(assetType == AssetType.CULVERT ? 3 : 1);
        }
        if (frequency.getMonthFrequency() == null || frequency.getMonthFrequency() == 0) {
            frequency.setMonthFrequency(assetType == AssetType.CULVERT ? 3 : 1);
        }
    }

    /**
     * 查找下一个有效的巡检记录
     *
     * @param checks     巡检记录列表
     * @param startIndex 开始查找的索引
     * @return 下一个有效记录的索引, 如果没有找到返回列表大小
     */
    private int findNextValidCheck(List<PatrolAssetCheck> checks, int startIndex) {
        for (int i = startIndex; i < checks.size(); i++) {
            PatrolAssetCheck check = checks.get(i);
            if (check.getType() != null && check.getCheckTime() != null) {
                return i;
            }
        }
        return checks.size();
    }

    // private void updateExpiry(BlockingQueue<PatrolDiseaseDTO> patrolDiseaseDTOS,
    //                           Set<String> updateAssetIds,
    //                           AssetType assetType) {
    //     String tableName = assetType.getTableName();
    //     List<PatrolDiseaseDTO> batch = new ArrayList<>();

    //     while (true) {
    //         try {
    //             PatrolDiseaseDTO dto = patrolDiseaseDTOS.poll(10, TimeUnit.MILLISECONDS);
    //             if (dto == null) {
    //                 continue;
    //             }

    //             // 如果收到结束标记，处理剩余的批次后退出
    //             if (dto == END_MARKER_DISEASE) {
    //                 if (!batch.isEmpty()) {
    //                     // 执行最后一批更新
    //                     log.info("批量更新{}条{}到期时间数据", batch.size(), assetType.name());
    //                     this.updateExpiryBatch(batch, tableName);
    //                     // 添加到已更新集合
    //                     batch.forEach(item -> updateAssetIds.add(item.getId()));
    //                 }
    //                 break;
    //             }

    //             // 添加到批次
    //             batch.add(dto);

    //             // 当批次达到100条时执行更新
    //             if (batch.size() >= 100) {
    //                 log.info("批量更新{}条{}到期时间数据", batch.size(), assetType.name());
    //                 this.updateExpiryBatch(batch, tableName);
    //                 // 添加到已更新集合
    //                 batch.forEach(item -> updateAssetIds.add(item.getId()));
    //                 // 清空批次
    //                 batch.clear();
    //             }

    //         } catch (InterruptedException e) {
    //             Thread.currentThread().interrupt();
    //             log.error("处理到期时间数据被中断", e);
    //             break;
    //         } catch (Exception e) {
    //             log.error("处理到期时间数据发生错误", e);
    //             // 发生错误时也尝试处理当前批次
    //             if (!batch.isEmpty()) {
    //                 try {
    //                     this.updateExpiryBatch(batch, tableName);
    //                     batch.forEach(item -> updateAssetIds.add(item.getId()));
    //                 } catch (Exception ex) {
    //                     log.error("处理剩余批次时发生错误", ex);
    //                 }
    //             }
    //             break;
    //         }
    //     }
    // }

    /**
     * 批量更新病害数量
     */
    private void updateExpiryBatch(List<PatrolDiseaseDTO> batch, String tableName) {
        if (batch == null || batch.isEmpty()) {
            return;
        }

        try {
            // 切换到从库
            DynamicDataSourceContextHolder.push("slave");
            // 执行批量更新
            baseMapper.updateExpiryBatch(batch, tableName);
        } finally {
            // 清理数据源上下文
            DynamicDataSourceContextHolder.clear();
        }
    }

    public void updatePropertyUnitName() {
        // 处理隧道资产
        AssetBaseDataRequest requestTunnel = new AssetBaseDataRequest();
        requestTunnel.setAssetType(AssetType.TUNNEL);
        requestTunnel.setDataRule(false);
        requestTunnel.setCheckStartTime(LocalDateTime.of(2025, 1, 1, 0, 0, 0));
        requestTunnel.setCustomSqlCondition("property_unit_name LIKE '%站'");
        List<PatrolAssetCheck> patrolAssetChecks = this.selectPatrolAssetCheck(requestTunnel, null, null, null);
        log.info("开始处理隧道资产数据，找到 {} 条隧管站需要修改为分处：隧", patrolAssetChecks.size());

        // 收集待更新的隧道资产
        List<PatrolAssetCheck> tunnelBatchList = new ArrayList<>();
        int tunnelCount = 0;
        for (PatrolAssetCheck patrolAssetCheck : patrolAssetChecks) {
            if (patrolAssetCheck.getPropertyUnitId() != null) {
                SysDept sysDept = remoteDeptAuthService
                        .getBranchOfficeByDeptId(Long.valueOf(patrolAssetCheck.getPropertyUnitId())).getData();
                if (sysDept != null) {
                    log.info("处理隧道资产: ID={}, 原名称={}, 新名称={}", patrolAssetCheck.getAssetId(),
                            patrolAssetCheck.getPropertyUnitName(), sysDept.getDeptName());
                    patrolAssetCheck.setPropertyUnitId(sysDept.getDeptId().toString());
                    patrolAssetCheck.setPropertyUnitName(sysDept.getDeptName());
                    tunnelBatchList.add(patrolAssetCheck);
                    tunnelCount++;

                    // 每收集100条记录执行一次批量更新
                    if (tunnelBatchList.size() >= 100) {
                        updatePropertyUnitBatch(tunnelBatchList, AssetType.TUNNEL.getTableName());
                        tunnelBatchList.clear();
                    }
                }
            }
        }
        // 处理剩余的隧道资产记录
        if (!tunnelBatchList.isEmpty()) {
            updatePropertyUnitBatch(tunnelBatchList, AssetType.TUNNEL.getTableName());
            tunnelBatchList.clear();
        }

        // 处理桥梁资产
        AssetBaseDataRequest requestBridge = new AssetBaseDataRequest();
        requestBridge.setAssetType(AssetType.BRIDGE);
        requestBridge.setDataRule(false);
        requestBridge.setCheckStartTime(LocalDateTime.of(2025, 1, 1, 0, 0, 0));
        requestBridge.setCustomSqlCondition("property_unit_name LIKE '%站'");
        List<PatrolAssetCheck> bridgeAssetChecks = this.selectPatrolAssetCheck(requestBridge, null, null, null);
        log.info("开始处理桥梁资产数据，找到 {} 条桥梁管理站需要修改为分处：桥", bridgeAssetChecks.size());

        // 收集待更新的桥梁资产
        List<PatrolAssetCheck> bridgeBatchList = new ArrayList<>();
        int bridgeCount = 0;
        for (PatrolAssetCheck patrolAssetCheck : bridgeAssetChecks) {
            if (patrolAssetCheck.getPropertyUnitId() != null) {
                SysDept sysDept = remoteDeptAuthService
                        .getBranchOfficeByDeptId(Long.valueOf(patrolAssetCheck.getPropertyUnitId())).getData();
                if (sysDept != null) {
                    log.info("处理桥梁资产: ID={}, 原名称={}, 新名称={}", patrolAssetCheck.getAssetId(),
                            patrolAssetCheck.getPropertyUnitName(), sysDept.getDeptName());
                    patrolAssetCheck.setPropertyUnitId(sysDept.getDeptId().toString());
                    patrolAssetCheck.setPropertyUnitName(sysDept.getDeptName());
                    bridgeBatchList.add(patrolAssetCheck);
                    bridgeCount++;

                    // 每收集100条记录执行一次批量更新
                    if (bridgeBatchList.size() >= 100) {
                        updatePropertyUnitBatch(bridgeBatchList, AssetType.BRIDGE.getTableName());
                        bridgeBatchList.clear();
                    }
                }
            }
        }
        // 处理剩余的桥梁资产记录
        if (!bridgeBatchList.isEmpty()) {
            updatePropertyUnitBatch(bridgeBatchList, AssetType.BRIDGE.getTableName());
            bridgeBatchList.clear();
        }

        // 处理涵洞资产
        AssetBaseDataRequest requestCulvert = new AssetBaseDataRequest();
        requestCulvert.setAssetType(AssetType.CULVERT);
        requestCulvert.setDataRule(false);
        requestCulvert.setCheckStartTime(LocalDateTime.of(2025, 1, 1, 0, 0, 0));
        requestCulvert.setCustomSqlCondition("property_unit_name LIKE '%站'");
        List<PatrolAssetCheck> culvertAssetChecks = this.selectPatrolAssetCheck(requestCulvert, null, null, null);
        log.info("开始处理涵洞资产数据，找到 {} 条涵洞管理站需要修改为分处：涵", culvertAssetChecks.size());

        // 收集待更新的涵洞资产
        List<PatrolAssetCheck> culvertBatchList = new ArrayList<>();
        int culvertCount = 0;
        for (PatrolAssetCheck patrolAssetCheck : culvertAssetChecks) {
            if (patrolAssetCheck.getPropertyUnitId() != null) {
                SysDept sysDept = remoteDeptAuthService
                        .getBranchOfficeByDeptId(Long.valueOf(patrolAssetCheck.getPropertyUnitId())).getData();
                if (sysDept != null) {
                    log.info("处理涵洞资产: ID={}, 原名称={}, 新名称={}", patrolAssetCheck.getAssetId(),
                            patrolAssetCheck.getPropertyUnitName(), sysDept.getDeptName());
                    patrolAssetCheck.setPropertyUnitId(sysDept.getDeptId().toString());
                    patrolAssetCheck.setPropertyUnitName(sysDept.getDeptName());
                    culvertBatchList.add(patrolAssetCheck);
                    culvertCount++;

                    // 每收集100条记录执行一次批量更新
                    if (culvertBatchList.size() >= 100) {
                        updatePropertyUnitBatch(culvertBatchList, AssetType.CULVERT.getTableName());
                        culvertBatchList.clear();
                    }
                }
            }
        }
        // 处理剩余的涵洞资产记录
        if (!culvertBatchList.isEmpty()) {
            updatePropertyUnitBatch(culvertBatchList, AssetType.CULVERT.getTableName());
            culvertBatchList.clear();
        }

        log.info("资产属性单位名称更新完成，共处理隧道: {}条, 桥梁: {}条, 涵洞: {}条",
                tunnelCount, bridgeCount, culvertCount);
    }

    /**
     * 批量更新管养分处ID和名称
     */
    public void updatePropertyUnitBatch(List<PatrolAssetCheck> list, String tableName) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        try {
            // 切换到从库
            DynamicDataSourceContextHolder.push("slave");
            // 执行批量更新
            baseMapper.updatePropertyUnitBatch(list, tableName);
        } finally {
            // 清理数据源上下文
            DynamicDataSourceContextHolder.clear();
        }
    }


    //endregion

    //region 高级批量审核功能

    /**
     * 批量完成资产审核
     *
     * @param request 批量审核请求参数
     * @return 更新记录数
     */
    @Override
    public int completeAuditInfo(BatchAuditRequest request) {
        // 1. 获取审核人信息
        R<SysUser> userResult = remoteUserService.findByUserId(request.getReviewerId());
        if (userResult.getCode() != 200 || userResult.getData() == null) {
            log.error("获取审核人信息失败，reviewerId: {}", request.getReviewerId());
            throw new RuntimeException("获取审核人信息失败: " + userResult.getMsg());
        }
        SysUser sysUser = userResult.getData();
        
        // 2. 获取各类资产ID，只获取assetTypes中指定的类型
        Map<AssetType, List<String>> assetIdMap = new HashMap<>();
        
        // 如果已提供checkIds，则不需要请求资产ID列表
        if (CollectionUtil.isEmpty(request.getCheckIds())) {
            Set<AssetType> assetTypes = request.getTypes().stream()
                    .map(InspectionType::getAssetType)
                    .collect(Collectors.toSet());
                
            // 创建存储所有Future的列表，方便后续等待所有任务完成
            List<CompletableFuture<Map.Entry<AssetType, List<String>>>> assetIdFutures = new ArrayList<>();
            
            // 只为assetTypes中包含的资产类型创建异步任务
            for (AssetType assetType : assetTypes) {
                CompletableFuture<Map.Entry<AssetType, List<String>>> future = CompletableFuture.supplyAsync(() -> {
                    // 使用assetType.getCode()获取对应的类型代码
                    R<List<String>> response = remoteDeptAuthService.findAssetIdListByDeptId(
                            Long.valueOf(request.getDeptId()), assetType.getCode());
                    return new AbstractMap.SimpleEntry<>(assetType, response.getData());
                });
                assetIdFutures.add(future);
            }
            
            // 等待所有异步任务完成，并将结果放入assetIdMap
            CompletableFuture.allOf(assetIdFutures.toArray(new CompletableFuture[0])).join();
            assetIdFutures.forEach(future -> {
                Map.Entry<AssetType, List<String>> entry = future.join();
                assetIdMap.put(entry.getKey(), entry.getValue());
            });
        }

        // 3. 设置阻塞队列用于并行处理
        final BlockingQueue<PatrolAssetCheck> updateQueue = new LinkedBlockingQueue<>();

        // 4. 启动处理线程
        AtomicInteger updateCount = new AtomicInteger(0);
        CompletableFuture<Void> processingFuture = CompletableFuture.runAsync(() ->
                processAuditUpdates(updateQueue, updateCount));

        // 5. 并行处理每种检查类型
        List<CompletableFuture<Void>> typeFutures = request.getTypes().stream()
                .map(type -> CompletableFuture.runAsync(() ->
                        processTypeAudit(type, assetIdMap, request.getCheckIds(), request.getCheckStartTime(), request.getCheckEndTime(), sysUser,
                                request.getExactAuditTime(), request.getMinDelayDays(), request.getMaxDelayDays(), request.getStatus(),
                                request.getStatusList(), updateQueue)))
                .collect(Collectors.toList());

        // 6. 等待所有类型处理完成，并添加结束标记
        CompletableFuture.allOf(typeFutures.toArray(new CompletableFuture[0]))
                .thenRun(() -> updateQueue.add(END_MARKER))
                .join();

        // 7. 等待处理线程完成
        processingFuture.join();

        return updateCount.get();
    }
    private void processTypeAudit(InspectionType type, Map<AssetType, List<String>> assetIdMap,Set<String> checkIds,
                                  LocalDateTime checkStartTime, LocalDateTime checkEndTime, SysUser sysUser,
                                  LocalDateTime exactAuditTime, Integer minDelayDays, Integer maxDelayDays,AuditStatusType status,
                                  List<Integer> statusList, BlockingQueue<PatrolAssetCheck> updateQueue) {
        AssetType assetType = type.getAssetType();
        
        // 构建请求
        AssetBaseDataRequest request = new AssetBaseDataRequest();
        if(CollectionUtil.isNotEmpty(checkIds)){
            // 如果有checkIds，直接使用它们，跳过资产ID验证
            request.setCheckIds(checkIds);
        }else{
            // 只有在没有checkIds时才进行资产ID验证
            List<String> assetIds = assetIdMap.get(assetType);
            
            if (CollectionUtil.isEmpty(assetIds)) {
                log.info("{} 资产类型没有找到资产ID", assetType.getDescription());
                return;
            }
            request.setAssetType(assetType);
            request.setType(type);
            request.setIds(assetIds);
            request.setCheckStartTime(checkStartTime);
            request.setCheckEndTime(checkEndTime);
//            request.setStage(1);
            request.setStatusList(statusList);
        }
        
        // 查询符合条件的检查记录
        List<PatrolAssetCheck> checks;
        try {
            DynamicDataSourceContextHolder.push("slave");
            checks = baseMapper.selectAssetCheckData(
                    request, null, null, assetType.getTableName());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        if (CollectionUtil.isEmpty(checks)) {
            log.info("{} 类型没有符合条件的检查记录", type.getDescription());
            return;
        }

        // 随机数生成器，用于生成随机延迟天数和时间
        Random random = new Random();

        // 处理每条记录
        for (PatrolAssetCheck check : checks) {
            // 设置审核人信息
            check.setKahunaId(sysUser.getUserId().toString());
            check.setKahunaName(sysUser.getNickName());
            check.setKahunaSign(sysUser.getSignId());

            // 设置审核时间
            if (exactAuditTime != null) {
                check.setAuditTime(Date.from(exactAuditTime.atZone(ZoneId.systemDefault()).toInstant()));
            } else {
                // 获取检查时间
                LocalDateTime checkTime = check.getCheckTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime();

                // 生成随机延迟天数
                int delayDays = random.nextInt(maxDelayDays - minDelayDays + 1) + minDelayDays;

                // 生成随机小时数 (9-17点)
                int hours = random.nextInt(9) + 9;
                int minutes = random.nextInt(60);

                // 计算审核时间 = 检查时间 + 随机延迟天数 + 随机时间
                LocalDateTime auditTime = checkTime.plusDays(delayDays)
                        .withHour(hours).withMinute(minutes).withSecond(0);

                check.setAuditTime(Date.from(auditTime.atZone(ZoneId.systemDefault()).toInstant()));
            }

            // 设置审核状态
            check.setStatus(status);

            // 添加到更新队列
            try {
                updateQueue.put(check);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("添加到更新队列时被中断", e);
            }
        }
    }

    private void processAuditUpdates(BlockingQueue<PatrolAssetCheck> updateQueue, AtomicInteger updateCount) {
        Map<String, List<PatrolAssetCheck>> typeMap = new HashMap<>();

        while (true) {
            try {
                PatrolAssetCheck check = updateQueue.poll(10, TimeUnit.MILLISECONDS);

                if (check == null) {
                    continue;
                }

                if (check == END_MARKER) {
                    // 处理剩余批次
                    for (Map.Entry<String, List<PatrolAssetCheck>> entry : typeMap.entrySet()) {
                        if (!entry.getValue().isEmpty()) {
                            log.info("批量更新{}条{}记录", entry.getValue().size(), entry.getKey());
                            updateAuditInfo(entry.getValue());
                            updateCount.addAndGet(entry.getValue().size());
                        }
                    }
                    break;
                }

                // 按资产类型分组
                String tableName = check.getType().getAssetType().getTableName();
                typeMap.computeIfAbsent(tableName, k -> new ArrayList<>()).add(check);

                // 当某类型达到批次大小时执行批量更新
                List<PatrolAssetCheck> typeBatch = typeMap.get(tableName);
                if (typeBatch.size() >= 100) {
                    log.info("批量更新{}条{}记录", typeBatch.size(), tableName);
                    updateAuditInfo(typeBatch);
                    updateCount.addAndGet(typeBatch.size());
                    typeMap.put(tableName, new ArrayList<>());
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("处理审核更新被中断", e);
                break;
            } catch (Exception e) {
                log.error("处理审核更新发生错误", e);
            }
        }
    }

    private void updateAuditInfo(List<PatrolAssetCheck> checks) {
        if (CollectionUtil.isEmpty(checks)) {
            return;
        }

        try {
            DynamicDataSourceContextHolder.push("slave");

            // 按资产类型分组批量更新
            Map<String, List<PatrolAssetCheck>> typeMap = checks.stream()
                    .collect(Collectors.groupingBy(check ->
                            check.getType().getAssetType().getTableName()));

            typeMap.forEach((tableName, checkList) -> {
                baseMapper.updateAuditInfoBatch(checkList, tableName);
            });
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }
    //endregion

    //region 赋值

    /**
     * 查询checkTime>expiry
     * ids
     * type
     * tableName
     */
    @Override
    public List<PatrolCheckBase> selectMaxExpiryByCheckTimeGreaterThanExpiry(AssetBaseDataRequest request) {
        return baseMapper.selectMaxExpiryByCheckTimeGreaterThanExpiry(request, request.getTableName());
    }

    /**
     * 设置病害数
     *
     * @param patrolAssetCheck 需要设置的巡查对象
     * @param <T>              巡查对象类型
     */
    @Override
    public <T extends PatrolAssetCheck> T setDiseaseNum(T patrolAssetCheck) {
        if (patrolAssetCheck == null) {
            return null;
        }
        List<PatrolAssetCheckDetail> diseaseList = patrolAssetCheck.getPatrolCheckDetailList();
        if (diseaseList == null || diseaseList.isEmpty()) {
            // 查询病害数
            diseaseList = patrolAssetCheckDetailService.selectByCheckId(
                    Collections.singletonList(patrolAssetCheck.getId()), patrolAssetCheck.getType().getAssetType().getTableName());
        }
        // diseaseList不为空时，计算defect不在排除列表中的数量
        if (CollectionUtil.isNotEmpty(diseaseList)) {
            long diseaseNum = diseaseList.stream()
                    .filter(disease -> {
                        // 去除所有空白字符后再进行比较
                        String defect = disease.getDefect();
                        String defectWithoutWhitespace = defect == null ? "" :
                                defect.replaceAll("[\\s\\p{Z}]", "");
                        return !EXCLUDED_DEFECTS.contains(defectWithoutWhitespace);
                    })
                    .count();
            patrolAssetCheck.setDiseaseCount((int) diseaseNum);
        } else {
            patrolAssetCheck.setDiseaseCount(0);
        }
        return patrolAssetCheck;
    }

    /**
     * 置巡查记录的签名URL
     * <p>
     * 处理流程:
     * 1. 并行处理操作员和负责人的签名
     * 2. 将签名ID转换为对应的URL
     * 3. 等待所有异步操作完成
     * <p>
     * 优化策略:
     * 1. 使用CompletableFuture实现并行处理提高性能
     * 2. 采用函数式编程处理签名URL
     *
     * @param patrolAssetCheck 需要设置签名URL的巡查记录
     * @return 设置完成的巡查记录
     * <p>
     * 示例:
     * Input: patrolAssetCheck.oprUserSign = "123,456"
     * Output: patrolAssetCheck.oprUserSignUrl = "http://example.com/123"
     */
    public <T extends PatrolAssetCheck> T setSignUrl(T patrolAssetCheck) {
        // 添加空值检查
        if (patrolAssetCheck == null) {
            return null;
        }

        // 并行处理操作员签名URL
        CompletableFuture<Void> oprSignFuture = CompletableFuture
                .runAsync(() -> processSignUrl(patrolAssetCheck.getOprUserSign(),
                        patrolAssetCheck::setOprUserSignUrl));
        // 并行处理负责人签名URL
        CompletableFuture<Void> kahunaSignFuture = CompletableFuture
                .runAsync(() -> processSignUrl(patrolAssetCheck.getKahunaSign(),
                        patrolAssetCheck::setKahunaSignUrl));

        // 等待所有异步操作完成后返回结果
        CompletableFuture.allOf(oprSignFuture, kahunaSignFuture).join();
        return patrolAssetCheck;
    }

    /**
     * 处理单个签名的URL转换
     * <p>
     * 处理流程:
     * 1. 验证签名ID字符串是否有效
     * 2. 将签名ID字符串分割为单个ID
     * 3. 通过远程服务获取每个ID对应的URL
     * 4. 使用setter设置URL
     * <p>
     * 错误处理:
     * 1. 空值和无效值处理
     * 2. 远程服务调用异常处理
     * 3. ID为"null"的特殊情况处理
     *
     * @param signIds   逗号分隔的签名ID字符串
     * @param urlSetter URL设置函数
     *                  <p>
     *                  示例:
     *                  Input: signIds = "123,456,null"
     *                  Process:
     //                  - 分割为["123","456","null"]
     //                  - 获取每个ID对应的URL
     //                  - 通过urlSetter设置URL
     */
    private void processSignUrl(String signIds, Consumer<String> urlSetter) {
        Optional.ofNullable(signIds)  // 处理空值情况
                .filter(StringUtils::isNotBlank)  // 过滤空字符串
                .map(ids -> Arrays.stream(ids.split(",")))  // 分割签名ID字符串
                .ifPresent(stream -> stream
                        .filter(Objects::nonNull)  // 过滤null值
                        .forEach(id -> {
                            // 特殊处理"null"字符串的情况
                            if ("null".equals(id)) {
                                urlSetter.accept(null);
                                return;
                            }
                            try {
                                // 通过远程服务获取文件URL并设置
                                Optional.ofNullable(remoteFileService.getFile(id))
                                        .map(R::getData)
                                        .map(SysFile::getUrl)
                                        .ifPresent(urlSetter);
                            } catch (Exception e) {
                                // 记录错误日志但继续处理其他ID
                                log.error("获取文件URL失败, fileId: {}", id, e);
                            }
                        }));
    }

    /**
     * 批量设置巡查记录的签名URL
     * <p>
     * 处理流程:
     * 1. 收集所有记录的签名ID并去重
     * 2. 批量获取签名URL
     * 3. 将URL设置回对应的记录
     * <p>
     * 优化策略:
     * 1. 批量获取URL减少网络请求
     * 2. 使用Map缓存URL结果
     * 3. 函数式处理简化代码
     *
     * @param patrolAssetCheckList 需要设置签名URL的巡查记录列表
     * @return 设置完成的巡查记录列表
     */
    @Override
    public <T extends PatrolAssetCheck> List<T> setSignUrlBatch(List<T> patrolAssetCheckList) {
        if (CollectionUtil.isEmpty(patrolAssetCheckList)) {
            return patrolAssetCheckList;
        }

        // 收集所有签名ID并去重
        Set<String> allSignIds = patrolAssetCheckList.stream()
                .flatMap(check -> Stream.of(
                        Optional.ofNullable(check.getOprUserSign()),
                        Optional.ofNullable(check.getKahunaSign())
                ))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .filter(StringUtils::isNotBlank)
                .flatMap(ids -> Arrays.stream(ids.split(",")))
                .filter(id -> !("null".equals(id) || StringUtils.isBlank(id)))
                .collect(Collectors.toSet());

        // 批量获取签名URL并构建映射关系
        Map<String, String> signUrlMap = new HashMap<>();
        try {
            R<List<SysFile>> response = remoteFileService.findFilesByOwnerIds(new ArrayList<>(allSignIds));
            Optional.ofNullable(response)
                    .map(R::getData)
                    .ifPresent(files -> files.forEach(file ->
                            Optional.ofNullable(file.getUrl())
                                    .ifPresent(url -> signUrlMap.put(file.getOwnerId(), url))
                    ));
        } catch (Exception e) {
            log.error("批量获取文件URL失败, signIds: {}", allSignIds, e);
        }

        // 为每条记录设置URL
        patrolAssetCheckList.forEach(check -> {
            // 设置操作员签名URL
            setSignUrlFromMap(check.getOprUserSign(), check.getOprUserName(), signUrlMap, check::setOprUserSignUrl, true);
            // 设置负责人签名URL
            setSignUrlFromMap(check.getKahunaSign(), check.getKahunaName(), signUrlMap, check::setKahunaSignUrl, true);
            // 设置桩号
            setStack(check);
        });

        return patrolAssetCheckList;
    }

    private void setSignUrlFromMap(String signIds, String name, Map<String, String> urlMap,
                                   Consumer<String> urlSetter, boolean firstOnly) {
        // 生成默认URL
        String defaultUrl = "https://zhyhpt.yciccloud.com/prod-api/file/generateImage?text=" + Optional.ofNullable(name).orElse("");

        if (StringUtils.isBlank(signIds)) {
            // signIds为null或空时，直接设置默认URL
            urlSetter.accept(defaultUrl);
            return;
        }

        String[] idArray = signIds.split(",");

        if (firstOnly) {
            // 只取第一个有效的签名URL
            String url = Arrays.stream(idArray)
                    .filter(id -> !("null".equals(id) || StringUtils.isBlank(id)))
                    .findFirst()
                    .map(urlMap::get)
                    .orElse(defaultUrl); // 如果没有有效的签名ID，使用默认URL

            urlSetter.accept(url);
        } else {
            // 获取所有有效的签名URL，用逗号连接
            String urls = Arrays.stream(idArray)
                    .filter(id -> !("null".equals(id) || StringUtils.isBlank(id)))
                    .map(id -> {
                        String url = urlMap.get(id);
                        return url != null ? url : defaultUrl; // 如果URL为null，使用默认URL
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(","));

            urlSetter.accept(StringUtils.isNotBlank(urls) ? urls : defaultUrl); // 如果没有有效URL，使用默认URL
        }
    }


    /**
     * 设置格式化的桩号
     *
     * @param patrolAssetCheck 巡查对象
     * @param <T>              巡查对象类型
     * @return 设置完成的巡查对象
     */
    public <T extends PatrolAssetCheck> T setStack(T patrolAssetCheck) {
        if (patrolAssetCheck == null) {
            return null;
        }
        patrolAssetCheck.setStakeFormat(StackUtils.formatStack(patrolAssetCheck.getCenterStake()));
        return patrolAssetCheck;
    }


    /**
     * 设置巡查对象的过期时间和巡检频率
     *
     * @param patrolAssetCheck 需要设置的巡查对象
     * @param <T>              巡查对象类型(桥梁、涵洞、隧道等)
     * @return 设置完成的巡查对象
     * <p>
     * 处理流程:
     * 1. 获取资产的巡检频率设置
     * 2. 计算下次巡检过期时间
     * 3. 根据资产类型处理特殊逻辑
     * <p>
     * 说明:
     * - 频率默认值为1
     * - 不同类型资产可能有特殊处理逻辑
     * - 返回null如果输入为null
     */
    public <T extends PatrolCheckBase> T setExpiryAndFrequency(T patrolAssetCheck) {
        // 参数有效性检查
        if (patrolAssetCheck == null) {
            return null;
        }

        // 获取巡检类型
        InspectionType inspectionType = patrolAssetCheck.getType();


        Integer frequency = patrolAssetCheck.getFrequency();
        if (frequency == null || frequency <= 0) {
            // 从频率设置服务获取该资产的巡检频率
            frequency = patrolFrequencySettingsService.
                    getFrequencyByAssetId(
                            patrolAssetCheck.getAssetId(),  // 资产ID
                            inspectionType.getAssetType().getCode(),  // 资产类型代码
                            inspectionType.getFlag()  // 巡检类型标志
                    );
        }
        // 设置默认频率为1
        frequency = FrequencyUtils.getFrequency(frequency, inspectionType.getAssetType());
        patrolAssetCheck.setFrequency(frequency);

        // 计算并设置过期时间
        LocalDateTime expiry = findExpiryResults(patrolAssetCheck);
        patrolAssetCheck.setExpiry(expiry);

        return patrolAssetCheck;
    }

    /**
     * 获取当年经常检查的所有记录
     *
     * @param request 资产ID
     * @return 当年经常检查的所有记录
     */
    public List<PatrolAssetCheck> getOftenCheckList(AssetBaseDataRequest request, Long pageNum, Long pageSize, AtomicInteger total) {
        if (request == null || request.getCheckTime() == null || request.getType() == null) {
            return null;
        }
        if (pageNum == null || pageSize == null) {
            baseCacheService.setDeptIds(request);
        } else {
            List<String> ids = baseCacheService.getBaseDataResponseIdByPage(request, pageNum, pageSize, total);
            if (CollectionUtil.isEmpty(ids)) {
                return null;
            }
            request.setIds(ids);
        }
        String year = request.getCheckTime().format(DateTimeFormatter.ofPattern("yyyy"));
        return patrolAssetCheckMapper.selectCheckedYear(request.getIds(), year, request.getType(), request.getAssetType().getTableName());
    }

    /**
     * 用于分组的记录类，包含巡检类型和日期
     * 作为Map的key使用，用于批量处理时的分组
     *
     * @param type 巡检类型(如日常巡检、经常巡检等)
     * @param date 巡检日期
     */
    private record CheckGroup(InspectionType type, LocalDate date) {
    }

    /**
     * 日期调整计算器
     * 用于根据巡检频率计算下一次或上一次的巡检日期
     *
     * @param flag true表示按天数调整，false表示按月份调整
     */
    private record ExpiryCalculator(boolean flag) {
        /**
         * 根据给定的频率调整日期
         *
         * @param date    待调整的基准日期
         * @param freq    调整的频率(天数或月数)
         * @param forward true表示向后调整(未来)，false表示向前调整(过去)
         * @return 调整后的日期
         * <p>
         * 调整规则:
         * 1. 按天调整(flag=true):
         * - 向后: date + freq天
         * - 向前: date - freq天
         * 2. 按月调整(flag=false):
         * - 向后: date + freq月的月末
         * - 向前: date - freq月的月末
         */
        LocalDate adjustDate(LocalDate date, int freq, boolean forward) {
            return flag ?
                    // 按天数调整
                    forward ? date.plusDays(freq) : date.minusDays(freq) :
                    // 按月份调整，并取月末日期
                    forward ? YearMonth.from(date.plusMonths(freq)).atEndOfMonth() :
                            YearMonth.from(date.minusMonths(freq)).atEndOfMonth();
        }
    }

    /**
     * 查找单个巡检记录的过期时间
     *
     * @param patrolAssetCheck 巡检记录
     * @return 计算得到的过期时间
     * <p>
     * 处理流程:
     * 1. 参数验证
     * 2. 提取必要信息(资产ID、巡检类型、频率等)
     * 3. 从从库查询历史记录
     * 4. 计算过期时间
     * <p>
     * 说明:
     * - 使用从库查询以减轻主库压力
     * - 频率默认值为1
     * - 返回null如果输入无效
     */
    private <T extends PatrolCheckBase> LocalDateTime findExpiryResults(T patrolAssetCheck) {
        // 参数有效性检查
        if (patrolAssetCheck == null || patrolAssetCheck.getCheckTime() == null) {
            return null;
        }

        // 提取必要信息
        String assetId = patrolAssetCheck.getAssetId();
        InspectionType inspectionType = patrolAssetCheck.getType();
        String tableName = inspectionType.getAssetType().getTableName();

        // 获取并验证频率，默认为1
        Integer frequency = patrolAssetCheck.getFrequency();
        frequency = FrequencyUtils.getFrequency(frequency, inspectionType.getAssetType());

        // 转换检查时间为LocalDate
        LocalDate date = patrolAssetCheck.getCheckTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 切换到从库查询历史记录
        DynamicDataSourceContextHolder.push("slave");
        PatrolCheckBase[] results = patrolAssetCheckMapper.findExpiryResults(
                assetId, tableName, inspectionType, date);
        DynamicDataSourceContextHolder.clear();

        // 计算并返回过期时间
        return this.calculateExpiry(results, date, inspectionType, frequency, patrolAssetCheck);
    }

    /**
     * 批量查找多个巡检记录的过期时间
     *
     * @param patrolAssetChecks 需要处理的巡检记录列表
     * @return Map<资产ID, 过期时间> 每个资产对应的过期时间
     * <p>
     * 处理流程:
     * 1. 按巡检类型和时间分组
     * 2. 并行处理每个分组提高性能
     * 3. 批量查询历史记录
     * 4. 计算过期时间
     * <p>
     * 优化策略:
     * - 使用分组减少数据库查询次数
     * - 使用并行流提高处理性能
     * - 使用从库查询减轻主库压力
     */
    private Map<String, LocalDateTime> findExpiryResultsBatch(List<? extends PatrolCheckBase> patrolAssetChecks) {
        // 检查输入列表是否为空
        if (CollectionUtil.isEmpty(patrolAssetChecks)) {
            return Map.of();
        }

        // 按照检查类型和检查时间分组
        // key: CheckGroup(type + date), value: 相同类型和日期的巡检记录列表
        Map<CheckGroup, List<PatrolCheckBase>> groupedChecks = patrolAssetChecks.stream()
                .collect(Collectors.groupingBy(check -> new CheckGroup(
                        check.getType(),
                        check.getCheckTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate()
                )));

        // 并行处理每个分组，提高性能
        return groupedChecks.entrySet().parallelStream()
                .<Map.Entry<String, LocalDateTime>>flatMap(entry -> {
                    CheckGroup group = entry.getKey();
                    List<PatrolCheckBase> checks = entry.getValue();

                    // 获取当前分组的所有资产ID
                    List<String> assetIds = checks.stream()
                            .map(PatrolCheckBase::getAssetId)
                            .toList();

                    // 切换到从库查询数据
                    DynamicDataSourceContextHolder.push("slave");
                    try {
                        // 批量查询历史巡检记录
                        List<PatrolCheckBase> resultList = patrolAssetCheckMapper.findExpiryResultsBatch(
                                assetIds,
                                group.type().getAssetType().getTableName(),
                                group.type(),
                                group.date()
                        );

                        // 处理查询结果并返回资产ID到过期时间的映射
                        return processResults(resultList, checks, group);
                    } finally {
                        // 确保清理数据源上下文，防止内存泄漏
                        DynamicDataSourceContextHolder.clear();
                    }
                })
                // 收集结果，转换为Map
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (a, b) -> a  // 处理重复key的策略：保留第一个值
                ));
    }

    /**
     * 处理批量查询的巡检记录结果，计算每个资产的过期时间
     *
     * @param resultList 数据库查询到的历史巡检记录列表
     * @param checks     当前需要处理的巡检记录列表
     * @param group      分组信息(包含检查类型和日期)
     * @return 处理后的结果流(资产ID - > 过期时间的映射)
     * <p>
     * 处理流程:
     * 1. 将历史记录按资产ID分组
     * 2. 为每个当前巡检记录计算过期时间
     * 3. 返回资产ID和过期时间的映射关系
     * <p>
     * 示例:
     * 输入:
     * - resultList: [asset1的历史记录1, asset1的历史记录2, asset2的历史记录1...]
     * - checks: [asset1的当前记录, asset2的当前记录...]
     * 输出: [(asset1, 过期时间1), (asset2, 过期时间2)...]
     */
    private Stream<Map.Entry<String, LocalDateTime>> processResults(
            List<PatrolCheckBase> resultList,
            List<PatrolCheckBase> checks,
            CheckGroup group) {

        // 将历史巡检记录按资产ID分组并转换为数组
        // key: 资产ID, value: 该资产的历史巡检记录数组
        Map<String, PatrolCheckBase[]> batchResults = resultList.stream()
                .collect(Collectors.groupingBy(
                        PatrolCheckBase::getAssetId,  // 按资产ID分组
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.toArray(PatrolCheckBase[]::new)  // 转换为数组
                        )
                ));

        // 处理每个当前巡检记录
        return checks.stream()
                .map(check -> Map.entry(
                        check.getAssetId(),  // 资产ID作为key
                        calculateExpiry(
                                batchResults.get(check.getAssetId()),  // 获取该资产的历史记录
                                group.date(),  // 分组日期
                                group.type(),  // 检查类型
                                Optional.ofNullable(check.getFrequency()).orElse(1),  // 频率(默认为1)
                                check  // 当前巡检记录
                        )
                ));
    }


    /**
     * 计算巡检记录的过期时间
     *
     * @param results        历史巡检记录数组，按时间降序排列
     * @param date           当前巡检日期
     * @param inspectionType 巡检类型(决定按天还是按月计算)
     * @param frequency      巡检频率(天数或月数)
     * @param check          当前巡检记录(用于更新频率)
     * @return 计算得到的过期时间
     * <p>
     * 计算策略:
     * 1. 无历史记录: 基于当前日期计算下一个过期时间
     * 2. 单条历史记录: 根据历史记录计算合适的过期时间
     * 3. 多条历史记录: 考虑历史记录间隔，避免冲突的过期时间
     * <p>
     * 示例:
     * - 按天计算(flag=true): 每5天巡检一次
     * - 按月计算(flag=false): 每3个月巡检一次
     */
    private LocalDateTime calculateExpiry(PatrolCheckBase[] results, LocalDate date,
                                          InspectionType inspectionType, int frequency,
                                          PatrolCheckBase check) {
        // 创建日期调整计算器，根据巡检类型决定按天还是按月调整
        ExpiryCalculator calculator = new ExpiryCalculator(inspectionType.getFlag());

        // 根据历史记录数量选择不同的计算策略
        return switch (results == null ? 0 : results.length) {
            // 无历史记录: 
            // 直接在当前日期基础上增加(频率-1)个周期，设置为当天最后一秒
            case 0 -> calculator.adjustDate(date, frequency - 1, true)
                    .atTime(LocalTime.of(23, 59, 59));

            // 单条历史记录:
            // 使用专门的处理方法计算过期时间
            case 1 -> calculateSingleResult(results != null ? results[0] : null, date, frequency, calculator);

            // 多条历史记录:
            // 使用更复杂的逻辑处理，考虑历史记录间的关系
            default -> results != null ? calculateMultipleResults(results, date, frequency, calculator, check) : 
                    calculator.adjustDate(date, frequency - 1, true).atTime(LocalTime.of(23, 59, 59));
        };
    }

    /**
     * 处理只有一条历史巡检记录时的过期时间计算
     *
     * @param result     历史巡检记录
     * @param date       目标日期
     * @param frequency  巡检频率(天数或月数)
     * @param calculator 日期调整计算器
     * @return 计算得到的过期时间
     * <p>
     * 计算逻辑:
     * 1. 如果目标日期在历史记录之前:
     * - 向前回溯直到找到小于目标日期的时间点
     * - 然后向后调整一次，确保找到第一个大于目标日期的值
     * 2. 如果目标日期在历史记录之后:
     * - 向后递增直到找到第一个大于目标日期的时间点
     * <p>
     * 示例:
     * - 历史记录: 2024-03-15
     * - 目标日期: 2024-03-10
     * - 频率: 5天
     * - 计算过程: 3-15 -> 3-10 -> 3-5 -> (回溯到3-10)
     */
    private LocalDateTime calculateSingleResult(PatrolCheckBase result, LocalDate date,
                                                int frequency, ExpiryCalculator calculator) {
        // 如果结果为空，返回默认值
        if (result == null || result.getExpiry() == null) {
            return calculator.adjustDate(date, frequency - 1, true)
                    .atTime(LocalTime.of(23, 59, 59));
        }
        
        // 获取历史记录的过期时间
        LocalDate recordExpiry = result.getExpiry().toLocalDate();
        LocalDate expiryDate = recordExpiry;

        if (date.isBefore(recordExpiry)) {
            // 目标日期在历史记录之前的情况
            // 向前回溯，直到找到小于目标日期的时间点
            while (date.isBefore(expiryDate)) {
                expiryDate = calculator.adjustDate(expiryDate, frequency, false);
            }
            // 如果回溯后的日期不等于原始记录日期
            // 向后调整一次，找到第一个大于目标日期的有效值
            if (!expiryDate.isEqual(recordExpiry)) {
                expiryDate = calculator.adjustDate(expiryDate, frequency, true);
            }
        } else {
            // 目标日期在历史记录之后的情况
            // 向后递增，直到找到第一个大于目标日期的时间点
            while (expiryDate.isBefore(date)) {
                expiryDate = calculator.adjustDate(expiryDate, frequency, true);
            }
        }

        // 返回过期时间，设置为当天的最后一秒
        return expiryDate.atTime(LocalTime.of(23, 59, 59));
    }

    /**
     * 处理有多条历史巡检记录时的过期时间计算
     *
     * @param results    历史巡检记录数组，按时间降序排列
     *                   results[0]: 最新记录
     *                   results[1]: 次新记录
     * @param date       目标日期
     * @param frequency  巡检频率
     * @param calculator 日期调整计算器
     * @param check      当前巡检记录(用于更新频率)
     * @return 计算得到的过期时间
     * <p>
     * 计算逻辑:
     * 1. 基于最新记录的过期时间和频率进行计算
     * 2. 检查是否与历史记录冲突
     * 3. 通过向前/向后调整找到合适的过期时间
     */
    private LocalDateTime calculateMultipleResults(PatrolCheckBase[] results, LocalDate date,
                                                   int frequency, ExpiryCalculator calculator,
                                                   PatrolCheckBase check) {
        // 获取最新记录和次新记录的过期时间
        LocalDate expiry1 = results[0].getExpiry().toLocalDate();
        LocalDate expiry2 = results[1].getExpiry().toLocalDate();

        // 比较两个过期时间,确定nextExpiry和lastExpiry
        LocalDate nextExpiry = expiry1.isAfter(expiry2) ? expiry1 : expiry2;
        LocalDate lastExpiry = expiry1.isAfter(expiry2) ? expiry2 : expiry1;
        int nextFrequency = Optional.ofNullable(results[0].getFrequency()).orElse(1);

        // 基于最新记录向前调整一个周期
        LocalDate expiryDate = calculator.adjustDate(nextExpiry, nextFrequency, false);

        // 检查是否与上一条记录冲突(新计算的过期时间不应早于次新记录的过期时间)
        if (!expiryDate.isAfter(lastExpiry)) {
            // 如果发生冲突，直接使用最新记录的过期时间
            return results[0].getExpiry();
        }

        // 向前查找，直到找到小于目标日期的时间点
        while (date.isBefore(expiryDate)) {
            expiryDate = calculator.adjustDate(expiryDate, nextFrequency, false);
        }

        // 如果计算结果与目标日期不相等，向后调整一次
        // 确保找到第一个大于目标日期的有效过期时间
        if (!expiryDate.isEqual(date)) {
            expiryDate = calculator.adjustDate(expiryDate, frequency, true);
        }

        // 更新当前巡检记录的频率为最新记录的频率
        check.setFrequency(nextFrequency);
        // 返回过期时间，设置为当天的最后一秒
        return expiryDate.atTime(LocalTime.of(23, 59, 59));
    }


    /**
     * 设置签名
     *
     * @param patrolAssetCheck 巡查对象
     * @return PatrolAssetCheck
     */
    @Override
    public <T extends PatrolAssetCheck> T setSign(T patrolAssetCheck) {
        String ids = patrolAssetCheck.getOprUserId();
        // 如果不为空 用逗号分割成List<Long>
        if (StringUtils.isNotBlank(ids)) {
            List<Long> idList = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());
            // 查询用户信息
            List<SysUser> sysUsers = remoteUserService.findListParam(idList).getData();
            // 如果不为空
            if (CollectionUtil.isNotEmpty(sysUsers)) {
                // 获取用户名称
                String signId = sysUsers.stream().map(SysUser::getSignId).collect(Collectors.joining(","));
                patrolAssetCheck.setOprUserSign(signId);
            }
        }
        return patrolAssetCheck;
    }
    //endregion

    //region 查询部分
    @Override
    public InspectionStatsVO assetCheckBaseInfo(AssetBaseDataRequest request) {
        try {
            InspectionStatsVO vo = new InspectionStatsVO();

            // 设置部门ID和请求类型
            baseCacheService.setDeptIds(request);
            request.setType(InspectionType.fromAssetType(request.getAssetType(), true));

            // 创建经常检查请求对象
            AssetBaseDataRequest oftenRequest = new AssetBaseDataRequest();
            BeanUtils.copyProperties(request, oftenRequest);
            oftenRequest.setType(InspectionType.fromAssetType(request.getAssetType(), false));
            oftenRequest.setIsCheck(true);

            // 获取日常检查数量
            DynamicDataSourceContextHolder.push("slave");
            try {
                int dailyCount = this.countAssetCheckData(request);
                vo.setDailyCount(dailyCount);
            } catch (Exception e) {
                log.error("获取日常检查数量失败, assetType: {}", request.getAssetType(), e);
                vo.setDailyCount(0);
            } finally {
                DynamicDataSourceContextHolder.clear();
            }

            // 获取经常检查数量
            DynamicDataSourceContextHolder.push("slave");
            try {
                int oftenCount = this.countAssetCheckData(oftenRequest);
                vo.setOftenCount(oftenCount);
            } catch (Exception e) {
                log.error("获取经常检查数量失败, assetType: {}", request.getAssetType(), e);
                vo.setOftenCount(0);
            } finally {
                DynamicDataSourceContextHolder.clear();
            }

            return vo;

        } catch (Exception e) {
            log.error("获取资产检查基础信息失败", e);
            // 返回空结果而不是抛出异常
            return InspectionStatsVO.builder()
                    .dailyCount(0)
                    .oftenCount(0)
                    .build();
        }
    }

    /**
     * 查询列表
     *
     * @param patrolAssetCheck 查询条件
     * @return List<PatrolAssetCheck>
     */
    @Override
    public List<PatrolAssetCheck> list(PatrolAssetCheck patrolAssetCheck) {
        QueryWrapper<PatrolAssetCheck> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        qw.setEntity(patrolAssetCheck);
        return this.list(qw);
    }

    /**
     * 获取request条件下的数据
     *
     * @param request 请求参数
     * @return 数据
     */
    public List<PatrolAssetCheck> selectAssetCheckData(AssetBaseDataRequest request, Long pageNum, Long pageSize) {
        if (request.getType() != null) request.setIsDailyCheck(request.getType().getFlag());
        return baseMapper.selectAssetCheckData(
                request, (pageNum - 1) * pageSize, pageSize, request.getAssetType().getTableName());
    }

    /**
     * 导出报表卡片
     */
    @Override
    public List<PatrolAssetCheck> exportReportCard(AssetBaseDataRequest request, Map<String, String> signUrlMap, Long pageNum, Long pageSize) {
        if (request.getType() != null) request.setIsDailyCheck(request.getType().getFlag());
        baseCacheService.setDeptIds(request);
        AssetType assetType = request.getAssetType();
        List<PatrolAssetCheck> result = new ArrayList<>();
        // 不分页查询处理
        if (pageNum == null || pageSize == null) {
            try {
                // 切换到从库查询
                DynamicDataSourceContextHolder.push("slave");
                result = baseMapper.selectAssetCheckDataWithDetail(
                        request, null, null, assetType.getTableName(), assetType.getDetailTableName());
            } finally {
                DynamicDataSourceContextHolder.clear();
            }
        } else {
            // 分页查询处理 - 并行执行总数查询和数据查询
            // 在异步任务中切换到从库
            DynamicDataSourceContextHolder.push("slave");
            try {
                result =
                        baseMapper.selectAssetCheckDataWithDetail(
                                request,
                                (pageNum - 1) * pageSize,
                                pageSize,
                                assetType.getTableName(),
                                assetType.getDetailTableName()
                        );
            } finally {
                DynamicDataSourceContextHolder.clear();
            }
        }

        // 处理签名相关信息
        Set<String> names = new HashSet<>();
        Set<String> ids = new HashSet<>();
        boolean addSortPrefix = request.getType().equals(InspectionType.BRIDGE_REGULAR_INSPECTION);
        patrolAssetCheckProcessor.processPatrolAssetCheckList(result, request.getType(), addSortPrefix);
        patrolAssetCheckProcessor.processAndPopulateOprUserSignList(result, names, ids);
        patrolAssetCheckProcessor.processAndPopulateKahunaSignLists(result, names, ids);

        // 获取签名URL
        ImageTextRequest imageTextRequest = new ImageTextRequest();
        imageTextRequest.setOwnerIdList(new ArrayList<>(ids));
        imageTextRequest.setNameList(new ArrayList<>(names));
        R<Map<String, String>> resultMap = remoteFileService.getUserSign(imageTextRequest);
        if (resultMap.getCode() != 200) {
            log.error("获取签名失败");
            return result;
        }

        // 获取签名URL映射
        signUrlMap.putAll(resultMap.getData());
        return result;
    }

    /**
     * 处理签名ID字符串，转换为ID列表
     *
     * @param signIdsStr 逗号分隔的签名ID字符串
     * @return 签名ID列表
     */
    // private List<String> processSignIdString(String signIdsStr) {
    //     if (signIdsStr == null || signIdsStr.trim().isEmpty()) {
    //         return new ArrayList<>();
    //     }

    //     return Arrays.stream(signIdsStr.split(","))
    //             .filter(id -> id != null && !id.trim().isEmpty())
    //             .collect(Collectors.toList());
    // }

    /**
     * 根据请求条件查询资产检查数据,支持分页和不分页两种模式
     *
     * @param request  资产基础数据请求对象,包含查询条件
     * @param pageNum  页码,为null时不分页
     * @param pageSize 每页大小,为null时不分页
     * @param total    用于返回总记录数的原子整数引用
     * @return 资产检查记录列表
     * <p>
     * 处理流程:
     * 1. 判断是否需要分页
     * 2. 不分页时直接查询全部数据
     * 3. 分页时并行执行总数统计和分页数据查询
     * 4. 等待所有异步任务完成并返回结果
     * <p>
     * 优化策略:
     * 1. 使用CompletableFuture实现异步并行查询
     * 2. 使用从库查询减轻主库压力
     * 3. 分页/不分页采用不同的查询策略
     * <p>
     * 示例:
     * 不分页查询:
     * selectPatrolAssetCheck(request, null, null, total)
     * -> 返回所有匹配记录
     * <p>
     * 分页查询:
     * selectPatrolAssetCheck(request, 1L, 10L, total)
     * -> 返回第1页的10条记录,total被设置为总记录数
     */
    public List<PatrolAssetCheck> selectPatrolAssetCheck(AssetBaseDataRequest request, Long pageNum, Long pageSize, AtomicInteger total) {
        if (request.getType() != null) request.setIsDailyCheck(request.getType().getFlag());
        if (total == null) {
            total = new AtomicInteger();
        }

        // 不分页查询处理
        if (pageNum == null || pageSize == null) {
            try {
                // 切换到从库查询
                DynamicDataSourceContextHolder.push("slave");
                List<PatrolAssetCheck> result = baseMapper.selectAssetCheckData(
                        request, null, null, request.getAssetType().getTableName());
                total.set(result.size());
                return result;
            } finally {
                DynamicDataSourceContextHolder.clear();
            }
        }


        // 分页查询处理 - 并行执行总数查询和数据查询
        // 在异步任务中切换到从库
        DynamicDataSourceContextHolder.push("slave");
        try {
            Integer count =
                    baseMapper.countAssetCheckData(request, request.getType().getAssetType().getTableName());
            total.set(count);
            List<PatrolAssetCheck> result =
                    baseMapper.selectAssetCheckData(
                            request,
                            (pageNum - 1) * pageSize,
                            pageSize,
                            request.getType().getAssetType().getTableName()
                    );
            return result;
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
//        // 分页查询处理
//        try {
//            // 创建总数查询的异步任务
//            CompletableFuture<Integer> countFuture = CompletableFuture.supplyAsync(() -> {
//                // 在异步任务中切换到从库
//                DynamicDataSourceContextHolder.push("slave");
//                try {
//                    return baseMapper.countAssetCheckData(request, request.getType().getAssetType().getTableName());
//                } finally {
//                    DynamicDataSourceContextHolder.clear();
//                }
//            });
//            System.out.println("------------------------------进入执行异步操作------------------------------");
//            // 创建数据查询的异步任务
//            CompletableFuture<List<PatrolAssetCheck>> dataFuture = CompletableFuture.supplyAsync(() -> {
//                // 在异步任务中切换到从库
//                DynamicDataSourceContextHolder.push("slave");
//                try {
//                    return baseMapper.selectAssetCheckData(
//                            request,
//                            (pageNum - 1) * pageSize,
//                            pageSize,
//                            request.getType().getAssetType().getTableName()
//                    );
//                } finally {
//                    DynamicDataSourceContextHolder.clear();
//                }
//            });
//
//            try {
//                // 等待两个查询都完成，设置超时时间为10秒
//                Integer count = countFuture.get(20, TimeUnit.SECONDS);
//                List<PatrolAssetCheck> result = dataFuture.get(20, TimeUnit.SECONDS);
//
//                // 设置总数
//                total.set(count);
//                return result;
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//                throw new RuntimeException("查询被中断", e);
//            } catch (TimeoutException e) {
//                throw new RuntimeException("查询超时(20s)", e);
//            } catch (ExecutionException e) {
//                throw new RuntimeException("查询执行异常", e);
//            }
//        } catch (Exception e) {
//            log.error("分页查询失败", e);
//            throw e;
//        }
    }

    /**
     * 获取request条件下的总数
     *
     * @param request 请求参数
     * @return 总数
     */
    public int countAssetCheckData(AssetBaseDataRequest request) {
        if (request.getType() != null) request.setIsDailyCheck(request.getType().getFlag());
        return baseMapper.countAssetCheckData(request, request.getAssetType().getTableName());
    }

    /**
     * 根据已经存在的PatrolAssetCheck创建一个新的PatrolAssetCheck
     *
     * @param params 查询条件
     * @return List<PatrolAssetCheck>
     */
    @Override
    public List<PatrolAssetCheck> findListByParam(Map<String, Object> params) {
        return patrolAssetCheckMapper.findListByParam(params);
    }

    /**
     * 根据资产id查询 PatrolAssetCheckDTO
     *
     * @param patrolAssetCheckRequest 查询条件
     * @return List<PatrolAssetCheckDTO></PatrolAssetCheckDTO>
     */
    @Override
    public List<PatrolAssetCheck> selectPatrolAssetChecksByAssetIds(PatrolAssetCheckRequest patrolAssetCheckRequest) {
        return patrolAssetCheckMapper.selectPatrolAssetChecksByAssetIds(patrolAssetCheckRequest);
    }

    /**
     * selectPatrolAssetChecksByAssetIdsBatch
     * 根据资产id查询 PatrolAssetCheckDTO
     *
     * @param patrolAssetCheckRequestList 查询条件List
     * @return List<PatrolAssetCheckDTO></PatrolAssetCheckDTO>
     */
    public List<PatrolAssetCheck> selectPatrolAssetChecksByAssetIdsBatch(
            List<PatrolAssetCheckRequest> patrolAssetCheckRequestList,
            AssetType assetType) {
        if (patrolAssetCheckRequestList == null || patrolAssetCheckRequestList.isEmpty()) {
            return null;
        }

        PatrolAssetCheckRequest patrolAssetCheckRequest = patrolAssetCheckRequestList.get(0);
        LocalDate nowCheckTime = patrolAssetCheckRequest.getNowCheckTime();
        String typeCode = patrolAssetCheckRequest.getType().getCode();

        if (assetType == null) {
            // assetType 为 null 时，将三个类型的检查结果并行合并
            DynamicDataSourceContextHolder.push("slave");
            List<PatrolAssetCheck> list = Arrays.stream(AssetType.values())
                    .parallel()
                    .flatMap(type -> baseMapper.selectPatrolAssetChecksByAssetIdsBatch(
                            patrolAssetCheckRequestList, nowCheckTime, typeCode, type.getTableName()
                    ).stream())
                    .collect(Collectors.toList());
            DynamicDataSourceContextHolder.clear();
            return list;
        } else {
            DynamicDataSourceContextHolder.push("slave");
            List<PatrolAssetCheck> list = baseMapper.selectPatrolAssetChecksByAssetIdsBatch(
                    patrolAssetCheckRequestList, nowCheckTime, typeCode, assetType.getTableName()
            );
            DynamicDataSourceContextHolder.clear();
            return list;
        }
    }

    //endregion


    //region 保存处理部分
//    @Transactional
    public void processExistingAssets(
            BlockingQueue<List<PatrolAssetCheckDetail>> existingAssetQueue,
            Set<String> updateAssetIds, AssetType assetType, String processId) {
        String processKey = PROCESS_KEY_PREFIX + processId;
        String completeKey = processKey + PROCESS_COMPLETE_SUFFIX;
        List<PatrolAssetCheckDetail> batch = new ArrayList<>();
        String tableName = assetType.getTableName();
        while (true) {
            try {
                List<PatrolAssetCheckDetail> details = existingAssetQueue.poll(10, TimeUnit.MILLISECONDS);
                if (details == END_MARKER_DETAIL) {
                    if (!batch.isEmpty()) {
                        log.info("批量插入{}条{}数据库", batch.size(), "patrolAssetCheckDetail");
                        patrolAssetCheckDetailService.saveAll(batch, tableName);
                        // 更新进度
                        updateProgress(processKey, batch.stream()
                                .map(PatrolAssetCheckDetail::getCheckId)
                                .collect(Collectors.toSet()));
                    }
                    // 标记该处理器完成
                    redisTemplate.opsForSet().add(completeKey, "EXISTING");
                    break;
                }
                if (details != null) {
                    batch.addAll(details);
                    details.forEach(detail -> updateAssetIds.add(detail.getCheckId()));
                    if (batch.size() >= 100) {
                        // 批量插入数据库
                        log.info("批量插入{}条{}数据库", batch.size(), "patrolAssetCheckDetail");
                        patrolAssetCheckDetailService.saveAll(batch, tableName);
                        // 更新进度
                        updateProgress(processKey, batch.stream()
                                .map(PatrolAssetCheckDetail::getCheckId)
                                .collect(Collectors.toSet()));
                        batch.clear();
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 处理PatrolAssetCheck和PatrolAssetCheckDetail
     *
     * @param nonExistingAssetQueue 阻塞队列
     * @param assetType             资产类型
     * @param flag                  true:日常检查 false:经常检查
     */
//    @Transactional
    public void processNonExistingAssets(BlockingQueue<PatrolAssetCheck> nonExistingAssetQueue,
                                         Set<String> updateAssetIds,
                                         AssetType assetType, Boolean flag, String processId) {
        String processKey = PROCESS_KEY_PREFIX + processId;
        String completeKey = processKey + PROCESS_COMPLETE_SUFFIX;
        List<PatrolAssetCheck> batch = new ArrayList<>();
        String tableName = assetType.getTableName();
        while (true) {
            try {
                PatrolAssetCheck po = nonExistingAssetQueue.poll(10, TimeUnit.MILLISECONDS);
                if (po == END_MARKER) {
                    if (!batch.isEmpty()) {
                        log.info("批量插入{}条{}数据库", batch.size(), "PatrolAssetCheck");
                        this.savePatrolAssetCheck(batch, tableName, flag);
                        // 更新进度
                        updateProgress(processKey, batch.stream()
                                .map(PatrolAssetCheck::getAssetId)
                                .collect(Collectors.toSet()));
                    }
                    // 标记该处理器完成
                    redisTemplate.opsForSet().add(completeKey, "NON_EXISTING");
                    break;
                }
                if (po != null) {
                    batch.add(po);
                    updateAssetIds.add(po.getAssetId());
                    if (batch.size() >= 100) {
                        // 批量插入数据库
                        log.info("批量插入{}条{}数据库", batch.size(), "PatrolAssetCheck");
                        this.savePatrolAssetCheck(batch, tableName, flag);
                        // 更新进度
                        updateProgress(processKey, batch.stream()
                                .map(PatrolAssetCheck::getAssetId)
                                .collect(Collectors.toSet()));
                        batch.clear();
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    // 更新处理进度
    private void updateProgress(String processKey, Set<String> ids) {
        if (!ids.isEmpty()) {
            redisTemplate.opsForSet().add(processKey, ids.toArray());
            // 设置过期时间
            redisTemplate.expire(processKey, PROCESS_EXPIRE_TIME, TimeUnit.HOURS);
            redisTemplate.expire(processKey + PROCESS_COMPLETE_SUFFIX, PROCESS_EXPIRE_TIME, TimeUnit.HOURS);
        }
    }

    /**
     * 获取处理进度
     *
     * @param processId 进程ID
     * @return Map<String, Object>
     */
    public Map<String, Object> getProgress(String processId) {
        String processKey = PROCESS_KEY_PREFIX + processId;
        String completeKey = processKey + PROCESS_COMPLETE_SUFFIX;

        Map<String, Object> result = new HashMap<>();

        // 获取已处理的ID列表
        Set<Object> processedIds = redisTemplate.opsForSet().members(processKey);
        List<String> processedIdList = processedIds != null ?
                processedIds.stream()
                        .map(Object::toString)
                        .collect(Collectors.toList()) :
                new ArrayList<>();

        result.put("processedIds", processedIdList);
        result.put("processedCount", processedIdList.size());

        // 获取完成状态
        Set<String> completedProcessors = Objects.requireNonNull(redisTemplate.opsForSet().members(completeKey)).stream()
                .map(Object::toString)
                .collect(Collectors.toSet());
        boolean isCompleted = completedProcessors.contains("EXISTING") && completedProcessors.contains("NON_EXISTING");
        result.put("isCompleted", isCompleted);

        return result;
    }

    /**
     * 保存PatrolAssetCheck和PatrolAssetCheckDetail
     *
     * @param patrolAssetCheckList PatrolAssetCheck
     * @param tableName            表名
     * @param flag                 true:日常检�� false:经常检查
     */
//    @Transactional
    public void savePatrolAssetCheck(List<PatrolAssetCheck> patrolAssetCheckList, String tableName, Boolean flag) {
        List<PatrolAssetCheckDetail> patrolAssetCheckDetailList = new ArrayList<>();

        if (flag) {
            patrolAssetCheckDetailList = patrolAssetCheckList.parallelStream()
                    .flatMap(patrolAssetCheck -> patrolAssetCheck.getPatrolCheckDetailList().stream())
                    .collect(Collectors.toList());
        }
        this.saveAll(patrolAssetCheckList, tableName);
        if (!patrolAssetCheckDetailList.isEmpty()) {
            patrolAssetCheckDetailService.saveAll(patrolAssetCheckDetailList, tableName);
        }
    }

    public void saveAll(List<PatrolAssetCheck> patrolAssetCheckList, String tableName) {
        DynamicDataSourceContextHolder.push("slave");
        baseMapper.insertBatch(patrolAssetCheckList, tableName);
        DynamicDataSourceContextHolder.clear();
    }

    //endregion

    //region 巡查日志部分

    /**
     * 生成缺少的检查日志
     *
     * @param assetBaseDataRequest 查询条件
     * @param nowDateList          当前日期列表
     * @return List<PatrolInspectionLogs> 缺少的检查日志
     */
    @Override
    public List<PatrolInspectionLogs> generateLogByCondition(AssetBaseDataRequest assetBaseDataRequest, List<LocalDate> nowDateList) {
        List<String> ids = baseCacheService.getBaseDataResponseId(assetBaseDataRequest);
        return autoGenerateLog(nowDateList, assetBaseDataRequest.getType(), ids);
    }

    /**
     * 自动生成检查方法
     *
     * @param nowDateList 当前日期列表
     * @param type        检查类型
     * @param ids         资产ID列表
     */
    private List<PatrolInspectionLogs> autoGenerateLog(List<LocalDate> nowDateList, InspectionType type, List<String> ids) {
        List<PatrolInspectionLogs> logsList = new ArrayList<>();
        // 使用 TreeMap 对 dateToAssetIdsMap 进行排序
        TreeMap<LocalDate, Map<LocalDate, List<String>>> sortedDateToAssetIdsMap = generateMap(nowDateList, type, ids, new HashMap<>());

        // 遍历排序后的 Map 并调用 autoGenerateCheck
        sortedDateToAssetIdsMap.forEach((nowDate, dateToAssetIdsMap) -> {
            List<PatrolInspectionLogs> logs = autoGenerateLogSingle(nowDate, type.getAssetType(), dateToAssetIdsMap);
            logsList.addAll(logs);
        });
        return logsList;
    }

    private List<PatrolInspectionLogs> autoGenerateLogSingle(LocalDate nowDate, AssetType assetType,
                                                             Map<LocalDate, List<String>> dateToAssetIdsMap) {
        /* 日常巡查 */
        final boolean flag = true;

        // 1. 查询现有的巡检记录
        List<PatrolAssetCheckRequest> patrolAssetCheckRequestList = dateToAssetIdsMap.entrySet().stream()
                .map(entry -> {
                    LocalDate date = entry.getKey();
                    List<String> assetIds = entry.getValue();
                    return new PatrolAssetCheckRequest(assetIds,
                            InspectionType.fromAssetType(assetType, flag), date, nowDate);
                })
                .toList();
        List<PatrolAssetCheck> patrolAssetCheckDTOList =
                this.selectPatrolAssetChecksByAssetIdsBatch(patrolAssetCheckRequestList, assetType);

        // 2. 获取需要新增的资产ID
        Set<String> existingAssetIds = patrolAssetCheckDTOList.stream()
                .map(PatrolAssetCheck::getAssetId)
                .collect(Collectors.toSet());

        List<String> generateAssetIds = new ArrayList<>();
        dateToAssetIdsMap.values().forEach(generateAssetIds::addAll);

        List<String> nonExistingAssetIdList = generateAssetIds.stream()
                .filter(id -> !existingAssetIds.contains(id))
                .collect(Collectors.toList());

        if (nonExistingAssetIdList.isEmpty()) {
            return Collections.emptyList();
        }

        // 3. 查询新增资产的基础数据
        AssetBaseDataRequest assetBaseDataRequest = new AssetBaseDataRequest();
        assetBaseDataRequest.setIds(nonExistingAssetIdList);
        assetBaseDataRequest.setAssetType(assetType);
        assetBaseDataRequest.setDataRule(false);
        List<?> assetResponseList = baseCacheService.selectBaseDataResponseByAssetIds(assetBaseDataRequest);

        // 使用CacheToDomainUtils工具类将缓存对象转换为BaseDataDomain对象
        List<BaseDataDomain> baseDataDomainList = CacheToDomainUtils.convertListLegacy(assetResponseList);

        // 4. 获取最早日期和路段ID列表
        LocalDate lastDate = dateToAssetIdsMap.keySet().stream()
                .min(Comparator.naturalOrder())
                .orElse(null);

        List<String> sectionIdList = baseDataDomainList.stream()
                .map(BaseDataDomain::getMaintenanceSectionId)
                .distinct()
                .collect(Collectors.toList());

        if (sectionIdList.size() > 100) {
            sectionIdList = null;
        }

        // 5. 查询现有巡检日志
        List<PatrolInspectionLogs> patrolInspectionLogsList =
                patrolInspectionLogsService.findLastOneByTime(lastDate, nowDate, sectionIdList);

        // 6. 找出缺失的路段ID并生成对应的巡检日志
        Set<String> existingSectionIds = patrolInspectionLogsList.stream()
                .map(PatrolInspectionLogs::getMaintenanceSectionId)
                .collect(Collectors.toSet());

        List<String> missingSectionIds = sectionIdList != null ? sectionIdList.stream()
                .filter(id -> !existingSectionIds.contains(id))
                .collect(Collectors.toList()) : new ArrayList<>();

        List<PatrolInspectionLogs> generatedLogs = missingSectionIds.stream()
                .map(id -> patrolInspectionLogsService.findLatestBySectionIdAndTime(id, lastDate, nowDate))
                .collect(Collectors.toList());

        return generatedLogs;
    }

    /**
     * 获取巡查日志实体list
     */
    // private List<PatrolInspectionLogs> autoGenerateLog(LocalDate nowDate, AssetType assetType, Map<LocalDate, List<String>>
    //         dateToAssetIdsMap) {
    //     /* 日常巡查 */
    //     final boolean flag = true;

    //     CompletableFuture<List<PatrolAssetCheck>> patrolAssetCheckDTOListFuture =
    //             CompletableFuture.supplyAsync(() -> {
    //                 // 根据dateToBridgeIdsMap的key生成patrolAssetCheckRequestDTOList
    //                 List<PatrolAssetCheckRequest> patrolAssetCheckRequestList = dateToAssetIdsMap.entrySet().stream()
    //                         .map(entry -> {
    //                             LocalDate date = entry.getKey();
    //                             List<String> assetIds = entry.getValue();
    //                             return new PatrolAssetCheckRequest(assetIds, InspectionType.fromAssetType(assetType, flag), date, nowDate);
    //                         })
    //                         .toList();
    //                 return this.selectPatrolAssetChecksByAssetIdsBatch(patrolAssetCheckRequestList, assetType);
    //             });

    //     CompletableFuture<List<BaseDataDomain>> assetResponseListFuture = patrolAssetCheckDTOListFuture.
    //             thenApply(patrolAssetCheckDTOList -> {
    //                 // 获取存在的 assetId 集合
    //                 Set<String> existingAssetIds = patrolAssetCheckDTOList.stream()
    //                         .map(PatrolAssetCheck::getAssetId)
    //                         .collect(Collectors.toSet());
    //                 // 获取不存在的 assetId 集合
    //                 List<String> nonExistingAssetIdList = new ArrayList<>();
    //                 // idList
    //                 List<String> generateAssetIds = new ArrayList<>();
    //                 // 遍历 dateToBridgeIdsMap，将所有桥梁ID添加到 generateBridgeIds
    //                 dateToAssetIdsMap.values().forEach(generateAssetIds::addAll);
    //                 generateAssetIds.forEach(id -> {
    //                     if (!existingAssetIds.contains(id)) {
    //                         nonExistingAssetIdList.add(id);
    //                     }
    //                 });
    //                 if (nonExistingAssetIdList.isEmpty()) {
    //                     return null;
    //                 }
    //                 AssetBaseDataRequest assetBaseDataRequest = new AssetBaseDataRequest();
    //                 assetBaseDataRequest.setIds(nonExistingAssetIdList);
    //                 assetBaseDataRequest.setAssetType(assetType);
    //                 assetBaseDataRequest.setDataRule(false);
    //                 // 根据不存在的assetId查询BridgeStaticResponse
    //                 List<?> assetResponseList = baseCacheService.selectBaseDataResponseByAssetIds(assetBaseDataRequest);
    //                 List<BaseDataDomain> baseDataDomainList = new ArrayList<>();
    //                 for (Object o : assetResponseList) {
    //                     BaseDataDomain baseDataDomain = new BaseDataDomain();
    //                     BeanUtils.copyProperties(o, baseDataDomain);
    //                     baseDataDomainList.add(baseDataDomain);
    //                 }
    //                 return baseDataDomainList;
    //             });

    //     CompletableFuture<List<PatrolInspectionLogs>> patrolInspectionLogsListFuture =
    //             assetResponseListFuture.thenApply((assetResponseList) -> {
    //                 if (assetResponseList == null) {
    //                     return Collections.emptyList();
    //                 }
    //                 // dateToBridgeIdsMap.keySet()最远的日期
    //                 LocalDate lastDate = dateToAssetIdsMap.keySet().stream()
    //                         .min(Comparator.naturalOrder()).orElse(null);

    //                 List<String> sectionIdList = assetResponseList.parallelStream()
    //                         .map(BaseDataDomain::getMaintenanceSectionId)
    //                         .distinct()
    //                         .collect(Collectors.toList());
    //                 if (sectionIdList.size() > 100) {
    //                     sectionIdList = null;
    //                 }
    //                 List<PatrolInspectionLogs> patrolInspectionLogsList =
    //                         patrolInspectionLogsService.findLastOneByTime(lastDate, nowDate, sectionIdList);

    //                 // 创建已存在的路段ID集合
    //                 Set<String> existingSectionIds = patrolInspectionLogsList.stream()
    //                         .map(PatrolInspectionLogs::getMaintenanceSectionId)
    //                         .collect(Collectors.toSet());

    //                 // 找出缺失的路段ID
    //                 List<String> missingSectionIds = sectionIdList.stream()
    //                         .filter(id -> !existingSectionIds.contains(id))
    //                         .collect(Collectors.toList());

    //                 // 为缺失的路段ID生成对应的PatrolInspectionLogs
    //                 return missingSectionIds.stream()
    //                         .map(id -> patrolInspectionLogsService.findLatestBySectionIdAndTime(id, lastDate, nowDate))
    //                         .collect(Collectors.toList());
    //             });

    //     // 等待所有异步任务完成

    //     return patrolInspectionLogsListFuture.join();
    // }
    //endregion

    //region 日常巡查部分

    // 条件查询后生成
    @Override
    public String generateCheckByCondition(AssetBaseDataRequest assetBaseDataRequest) {
        if (assetBaseDataRequest.getType() == null) {
            throw new IllegalArgumentException("巡查类型不能为空");
        }

        // 如果有日志ID，根据日志ID查询
        if (assetBaseDataRequest.getLogId() != null) {
            PatrolInspectionLogs patrolInspectionLogs =
                    patrolInspectionLogsService.getById(assetBaseDataRequest.getLogId());
            if (patrolInspectionLogs == null) {
                throw new IllegalArgumentException("巡查日志不存在");
            }
            InspectionType inspectionType = assetBaseDataRequest.getType();
            String logId = assetBaseDataRequest.getLogId();
            Long userId = assetBaseDataRequest.getRoleUserId();
            String userName = assetBaseDataRequest.getUserName();
            assetBaseDataRequest = new AssetBaseDataRequest();
            assetBaseDataRequest.setType(inspectionType);
            assetBaseDataRequest.setMaintenanceSectionId(patrolInspectionLogs.getMaintenanceSectionId());
            assetBaseDataRequest.setNowDateList(
                    Collections.singletonList(
                            patrolInspectionLogs
                                    .getCollectTime().toInstant().atZone(ZoneId.systemDefault())
                                    .toLocalDate()));
            assetBaseDataRequest.setLogId(logId);
            assetBaseDataRequest.setRoleUserId(userId);
            assetBaseDataRequest.setUserName(userName);
        }

        if (assetBaseDataRequest.getNowDateList() == null) {
            throw new IllegalArgumentException("巡查日期不能为空");
        }

        String processId = UUID.randomUUID().toString();

        Map<String, Object> localMap = SecurityContextHolder.getLocalMap();

        // 使用CompletableFuture异步执行
        AssetBaseDataRequest finalAssetBaseDataRequest = assetBaseDataRequest;
//        CompletableFuture.runAsync(() -> {
//            try {
//                SecurityContextHolder.setLocalMap(localMap);
//                List<String> ids = baseCacheService.getBaseDataResponseId(finalAssetBaseDataRequest);
//                autoGenerateCheck(
//                        finalAssetBaseDataRequest.getNowDateList(),
//                        finalAssetBaseDataRequest.getType(),
//                        ids,
//                        processId,
//                        finalAssetBaseDataRequest.getLogId(),
//                        finalAssetBaseDataRequest.getUserName()
//                );
//            } catch (Exception e) {
//                log.error("异步生成巡查任务失败, processId: {}", processId, e);
//                // 可以考虑将错误信息存入Redis，供前端查询状态时获取
//                redisService.setCacheObject("check:generation:error:" + processId,
//                        e.getMessage(), 24L, TimeUnit.HOURS);
//            }
//        });
        try {
            SecurityContextHolder.setLocalMap(localMap);
            List<String> ids = baseCacheService.getBaseDataResponseId(finalAssetBaseDataRequest);
            autoGenerateCheck(
                    finalAssetBaseDataRequest.getNowDateList(),
                    finalAssetBaseDataRequest.getType(),
                    ids,
                    processId,
                    finalAssetBaseDataRequest.getLogId(),
                    finalAssetBaseDataRequest.getUserName()
            );
        } catch (Exception e) {
            log.error("生成巡查任务失败, processId: {}", processId, e);
            // 存储错误信息到Redis，供前端查询状态时获取
            redisService.setCacheObject("check:generation:error:" + processId,
                    e.getMessage(), 24L, TimeUnit.HOURS);
        }
        return processId;
    }

    /**
     * 自动生成检查方法
     *
     * @param nowDateList 当前日期列表
     * @param type        检查类型
     * @param ids         资产ID列表
     */
    private void autoGenerateCheck(List<LocalDate> nowDateList, InspectionType type,
                                   List<String> ids, String processId, String logId, String userName) {
        // 资产ID到天频率的映射
        Map<String, Integer> assetIdToDayFrequencyMap = new HashMap<>();
        // 使用 TreeMap 对 dateToAssetIdsMap 进行排序
        TreeMap<LocalDate, Map<LocalDate, List<String>>> sortedDateToAssetIdsMap =
                generateMap(nowDateList, type, ids, assetIdToDayFrequencyMap);

        // 遍历排序后的 Map 并调用 autoGenerateCheck
        sortedDateToAssetIdsMap.forEach((date, assetIdsMap) ->
                runGenerateCheckSingle(date, type.getAssetType(),
                        assetIdsMap, assetIdToDayFrequencyMap, processId, logId, userName)
        );
    }


    /**
     * 生成自动生成的<日期, <日期, 资产ID列表>>映射
     */
    private TreeMap<LocalDate, Map<LocalDate, List<String>>> generateMap(
            List<LocalDate> nowDateList, InspectionType type,
            List<String> ids, Map<String, Integer> assetIdToDayFrequencyMap) {
        if (type == null) {
            throw new IllegalArgumentException("assetType不能为空");
        }
        // 日期到资产ID的映射
        Map<LocalDate, Map<LocalDate, List<String>>> dateToAssetIdsMap = new HashMap<>();

        nowDateList.forEach(nowDate -> {
            List<String> noGenerateIds = filterIdsByDate(nowDate, dateToAssetIdsMap, ids);
            Map<LocalDate, Map<LocalDate, List<String>>> dateToAssetMap =
                    generaByAssetIdList(nowDate, type, noGenerateIds, assetIdToDayFrequencyMap);
            mergeMap(dateToAssetIdsMap, dateToAssetMap);
        });

        // 使用 TreeMap 对 dateToAssetIdsMap 进行排序
        return new TreeMap<>(dateToAssetIdsMap);

    }

    /**
     * 根据日期过滤ID
     *
     * @param nowDate   当前日期
     * @param filterMap 过滤Map
     * @param ids       ID列表
     * @return 过滤后的ID列表
     */
    private List<String> filterIdsByDate(LocalDate nowDate,
                                         Map<LocalDate, Map<LocalDate, List<String>>> filterMap, List<String> ids) {
        if (filterMap == null || filterMap.isEmpty()) {
            return new ArrayList<>(ids);
        }

        return ids.stream()
                .filter(id -> !filterMap.entrySet().stream()
                        .filter(entry -> !entry.getKey().isBefore(nowDate))
                        .flatMap(entry -> entry.getValue().entrySet().stream())
                        .filter(innerEntry -> !innerEntry.getKey().isAfter(nowDate))
                        .anyMatch(innerEntry -> innerEntry.getValue().contains(id))
                )
                .collect(Collectors.toList());
    }

    /**
     * 合并两个日期资产映射Map
     *
     * @param targetMap 目标Map，合并结果将存储在此
     * @param sourceMap 源Map，需要被合并的数据
     */
    private void mergeMap(
            Map<LocalDate, Map<LocalDate, List<String>>> targetMap,
            Map<LocalDate, Map<LocalDate, List<String>>> sourceMap) {
        if (sourceMap == null || sourceMap.isEmpty()) {
            return;
        }

        sourceMap.forEach((outerDate, innerMap) -> {
            Map<LocalDate, List<String>> targetInnerMap = targetMap.computeIfAbsent(outerDate, k -> new HashMap<>());

            innerMap.forEach((innerDate, assetIds) -> {
                if (assetIds != null && !assetIds.isEmpty()) {
                    targetInnerMap.merge(
                            innerDate,
                            new ArrayList<>(assetIds),
                            (existingList, newList) -> {
                                Set<String> combinedSet = new LinkedHashSet<>(existingList);
                                combinedSet.addAll(newList);
                                return new ArrayList<>(combinedSet);
                            }
                    );
                }
            });
        });
    }

    /**
     * 传入查询条件PatrolAssetCheckRequest，查询PatrolAssetCheckDTO
     */
//    @Transactional
    @Override
    public void scheduleGenerateCheck(LocalDate nowDate, AssetType assetType, String processId) {
        if (assetType == null) {
            throw new IllegalArgumentException("assetType不能为空");
        }

        // 资产ID到天频率的映射
        Map<String, Integer> assetIdToDayFrequencyMap = new HashMap<>();

        // 上次生成到资产ID列表的映射
        Map<LocalDate, Map<LocalDate, List<String>>> dateToAssetIdsMap =
                patrolInspectionLastService.generatedAtByAssetIdList(nowDate, assetType, assetIdToDayFrequencyMap);
        // 使用 TreeMap 对 dateToAssetIdsMap 进行排序
        TreeMap<LocalDate, Map<LocalDate, List<String>>> sortedDateToAssetIdsMap = new TreeMap<>(dateToAssetIdsMap);
        // 遍历排序后的 Map 并调用 runGenerateCheck
        sortedDateToAssetIdsMap.forEach((date, assetIdsMap) -> {
            runGenerateCheck(date, assetType, assetIdsMap, assetIdToDayFrequencyMap, processId, null, null);
        });
    }


    public void runGenerateCheckSingle(LocalDate nowDate, AssetType assetType, Map<LocalDate, List<String>>
                                               dateToAssetIdsMap, Map<String, Integer> assetIdToDayFrequencyMap,
                                       String processId, String logId, String userName) {
        /* 日常巡查 */
        final boolean flag = true;

        // 更新的数据
        Set<String> updateAssetIds = new HashSet<>();

        // 1. 查询现有的巡检记录
        List<PatrolAssetCheckRequest> patrolAssetCheckRequestList = dateToAssetIdsMap.entrySet().stream()
                .map(entry -> {
                    LocalDate date = entry.getKey();
                    List<String> assetIds = entry.getValue();
                    return new PatrolAssetCheckRequest(
                            assetIds, InspectionType.fromAssetType(assetType, flag), date, nowDate);
                })
                .toList();
        List<PatrolAssetCheck> patrolAssetCheckDTOList = this.selectPatrolAssetChecksByAssetIdsBatch(
                patrolAssetCheckRequestList, assetType);

        // 2. 获取需要新增的资产ID
        Set<String> existingAssetIds = patrolAssetCheckDTOList.stream()
                .map(PatrolAssetCheck::getAssetId)
                .collect(Collectors.toSet());
        List<String> generateAssetIds = new ArrayList<>();
        dateToAssetIdsMap.values().forEach(generateAssetIds::addAll);
        List<String> nonExistingAssetIdList = generateAssetIds.stream()
                .filter(id -> !existingAssetIds.contains(id))
                .collect(Collectors.toList());

        // 3. 查询新增资产的基础数据
        List<BaseDataDomain> assetResponseList = new ArrayList<>();
        if (!nonExistingAssetIdList.isEmpty()) {
            AssetBaseDataRequest assetBaseDataRequest = new AssetBaseDataRequest();
            assetBaseDataRequest.setIds(nonExistingAssetIdList);
            assetBaseDataRequest.setAssetType(assetType);
            assetBaseDataRequest.setDataRule(false);
            List<?> responseList = baseCacheService.selectBaseDataResponseByAssetIds(assetBaseDataRequest);
            assetResponseList = CacheToDomainUtils.convertListLegacy(responseList);
        }

        // 4. 获取巡检日志
        Map<String, PatrolInspectionLogs> patrolInspectionLogsMap;
        if (logId != null) {
            PatrolInspectionLogs patrolInspectionLogs = patrolInspectionLogsService.selectById(logId);
            if (patrolInspectionLogs == null) {
                patrolInspectionLogsMap = Collections.emptyMap();
            } else {
                patrolInspectionLogsMap = Collections.singletonMap(
                        patrolInspectionLogs.getMaintenanceSectionId(), patrolInspectionLogs);
            }
        } else {
            LocalDate lastDate = dateToAssetIdsMap.keySet().stream()
                    .min(Comparator.naturalOrder()).orElse(null);
            List<String> sectionIdList = assetResponseList.stream()
                    .map(BaseDataDomain::getMaintenanceSectionId)
                    .distinct()
                    .collect(Collectors.toList());
            if (sectionIdList.size() > 100) {
                sectionIdList = null;
            }
            List<PatrolInspectionLogs> patrolInspectionLogsList =
                    patrolInspectionLogsService.findLastOneByTime(lastDate, nowDate, sectionIdList);
            patrolInspectionLogsMap = patrolInspectionLogsList.stream()
                    .collect(Collectors.toMap(PatrolInspectionLogs::getMaintenanceSectionId, logs -> logs, (logs1, logs2) -> {
                        if (logs1.getStartTime() == null) {
                            return logs2;
                        }
                        if (logs2.getStartTime() == null) {
                            return logs1;
                        }
                        return logs1.getStartTime().after(logs2.getStartTime()) ? logs1 : logs2;
                    }));
        }

        // 5. 处理现有资产的巡检记录
        for (PatrolAssetCheck dto : patrolAssetCheckDTOList) {
            PatrolInspectionLogs log = patrolInspectionLogsMap.get(dto.getMaintenanceSectionId());
            List<PatrolAssetCheckDetail> details = handleExistingAssets(dto, log.getId(), userName);
            if (details != null && !details.isEmpty()) {
                patrolAssetCheckDetailService.saveAll(details, assetType.getTableName());
                updateAssetIds.add(dto.getAssetId());
            }
        }

        // 6. 处理新增资产的巡检记录
        Map<String, LocalDate> assetIdToDateMap = new HashMap<>();
        dateToAssetIdsMap.forEach((date, assetIds) ->
                assetIds.forEach(assetId -> assetIdToDateMap.put(assetId, date)));

        List<PatrolAssetCheck> batch = new ArrayList<>();
        for (BaseDataDomain response : assetResponseList) {
            String maintenanceSectionId = response.getMaintenanceSectionId();
            PatrolInspectionLogs patrolInspectionLogs = patrolInspectionLogsMap.get(maintenanceSectionId);
            if (patrolInspectionLogs != null) {
                Date collectTime = patrolInspectionLogs.getCollectTime();
                LocalDate collectDate = collectTime.toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate date = assetIdToDateMap.get(response.getAssetId());
                if (date != null && collectDate.isAfter(date)) {
                    PatrolAssetCheck check = handleNonExistingAssets(
                            response, patrolInspectionLogs,
                            assetIdToDayFrequencyMap.get(response.getAssetId()),
                            nowDate,
                            InspectionType.fromAssetType(assetType, flag),
                            userName);
                    batch.add(check);
                    updateAssetIds.add(response.getAssetId());

                    // 当batch大小达到100时执行批量保存
                    if (batch.size() >= 100) {
                        this.savePatrolAssetCheck(batch, assetType.getTableName(), flag);
                        batch.clear();
                    }
                }
            }
        }

        // 处理剩余的数据
        if (!batch.isEmpty()) {
            this.savePatrolAssetCheck(batch, assetType.getTableName(), flag);
        }

        // 7. 更新生成时间
        if (!updateAssetIds.isEmpty()) {
            Integer updateNum = patrolInspectionLastService.updateGeneratedAtByAssetIdList(
                    new ArrayList<>(updateAssetIds), assetType, nowDate.atTime(LocalTime.of(23, 59, 59)), null);
            log.info(String.format("更新generatedAt数量：%d", updateNum));
        } else {
            log.info("无更新数据");
        }
    }


    /**
     * 生成日常巡查
     *
     * @param nowDate                  当前日期
     * @param assetType                资产类型
     * @param dateToAssetIdsMap        日期到资产ID的映射
     * @param assetIdToDayFrequencyMap 资产ID到天频率的映射
     */

    //    @Transactional
    public void runGenerateCheck(LocalDate nowDate, AssetType assetType, Map<LocalDate, List<String>>
                                         dateToAssetIdsMap, Map<String, Integer> assetIdToDayFrequencyMap,
                                 String processId, String logId, String userName) {
        /* 日常巡查 */
        final boolean flag = true;

        // 阻塞队列
        final BlockingQueue<List<PatrolAssetCheckDetail>> existingAssetQueue = new LinkedBlockingQueue<>();
        final BlockingQueue<PatrolAssetCheck> nonExistingAssetQueue = new LinkedBlockingQueue<>();

        // 更新的数据
        Set<String> updateAssetIds = ConcurrentHashMap.newKeySet();


        // 启动处理线程
        CompletableFuture<Void> existingAssetsProcessing =
                CompletableFuture.runAsync(() -> processExistingAssets(
                        existingAssetQueue, updateAssetIds, assetType, processId));
        CompletableFuture<Void> nonExistingAssetsProcessing =
                CompletableFuture.runAsync(() -> processNonExistingAssets(
                        nonExistingAssetQueue, updateAssetIds, assetType, flag, processId));


        CompletableFuture<List<PatrolAssetCheck>> patrolAssetCheckDTOListFuture =
                CompletableFuture.supplyAsync(() -> {
                    // 根据dateToBridgeIdsMap的key生成patrolAssetCheckRequestDTOList
                    List<PatrolAssetCheckRequest> patrolAssetCheckRequestList =
                            dateToAssetIdsMap.entrySet().stream()
                                    .map(entry -> {
                                        LocalDate date = entry.getKey();
                                        List<String> assetIds = entry.getValue();
                                        return new PatrolAssetCheckRequest(
                                                assetIds, InspectionType.fromAssetType(assetType, flag), date, nowDate);
                                    })
                                    .toList();
                    return this.selectPatrolAssetChecksByAssetIdsBatch(patrolAssetCheckRequestList, assetType);
                });

        CompletableFuture<List<BaseDataDomain>> assetResponseListFuture = patrolAssetCheckDTOListFuture.
                thenApply(patrolAssetCheckDTOList -> {
                    // 获取存在的 assetId 集合
                    Set<String> existingAssetIds = patrolAssetCheckDTOList.stream()
                            .map(PatrolAssetCheck::getAssetId)
                            .collect(Collectors.toSet());
                    // 获取不存在的 assetId 集合
                    List<String> nonExistingAssetIdList = new ArrayList<>();
                    // idList
                    List<String> generateAssetIds = new ArrayList<>();
                    // 遍历 dateToBridgeIdsMap，将所有桥梁ID添加到 generateBridgeIds
                    dateToAssetIdsMap.values().forEach(generateAssetIds::addAll);
                    generateAssetIds.forEach(id -> {
                        if (!existingAssetIds.contains(id)) {
                            nonExistingAssetIdList.add(id);
                        }
                    });
                    if (nonExistingAssetIdList.isEmpty()) {
                        return null;
                    }
                    AssetBaseDataRequest assetBaseDataRequest = new AssetBaseDataRequest();
                    assetBaseDataRequest.setIds(nonExistingAssetIdList);
                    assetBaseDataRequest.setAssetType(assetType);
                    assetBaseDataRequest.setDataRule(false);
                    // 根据不存在的assetId查询BridgeStaticResponse
                    List<?> assetResponseList = baseCacheService.selectBaseDataResponseByAssetIds(assetBaseDataRequest);
                    // 使用CacheToDomainUtils工具类将缓存对象转换为BaseDataDomain对象
                    return CacheToDomainUtils.convertListLegacy(assetResponseList);
                });

        CompletableFuture<Map<String, PatrolInspectionLogs>> patrolInspectionLogsListFuture =
                assetResponseListFuture.thenApply((assetResponseList) -> {
                    if (logId != null) {
                        PatrolInspectionLogs patrolInspectionLogs = patrolInspectionLogsService.selectById(logId);
                        if (patrolInspectionLogs == null) {
                            return Collections.emptyMap();
                        }
                        return Collections.singletonMap(patrolInspectionLogs.getMaintenanceSectionId(), patrolInspectionLogs);
                    } else {
                        // dateToBridgeIdsMap.keySet()最远的日期
                        LocalDate lastDate = dateToAssetIdsMap.keySet().stream()
                                .min(Comparator.naturalOrder()).orElse(null);

                        List<String> sectionIdList = assetResponseList.parallelStream()
                                .map(BaseDataDomain::getMaintenanceSectionId)
                                .distinct()
                                .collect(Collectors.toList());
                        if (sectionIdList.size() > 100) {
                            sectionIdList = null;
                        }
                        List<PatrolInspectionLogs> patrolInspectionLogsList =
                                patrolInspectionLogsService.findLastOneByTime(lastDate, nowDate, sectionIdList);
                        // 养护单位id到PatrolInspectionLogs的映射
                        return patrolInspectionLogsList.stream()
                                .collect(Collectors.toMap(PatrolInspectionLogs::getMaintenanceSectionId, logs -> logs, (logs1, logs2) -> {
                                    if (logs1.getStartTime() == null) {
                                        return logs2;
                                    }
                                    if (logs2.getStartTime() == null) {
                                        return logs1;
                                    }
                                    return logs1.getStartTime().after(logs2.getStartTime()) ? logs1 : logs2;
                                }));
                    }
                });

        CompletableFuture<List<CompletableFuture<Void>>> existingAssetsFuture = patrolAssetCheckDTOListFuture.thenCombine(
                patrolInspectionLogsListFuture, (patrolAssetCheckDTOList, patrolInspectionLogsMap) -> {
                    return patrolAssetCheckDTOList.stream()
                            .map(dto -> handleExistingAssetsAsync(
                                    dto, existingAssetQueue, patrolInspectionLogsMap.get(dto.getMaintenanceSectionId()), userName))
                            .collect(Collectors.toList());
                });

        CompletableFuture<List<CompletableFuture<Void>>> noExistingAssetsFuture = assetResponseListFuture.thenCombine(
                patrolInspectionLogsListFuture, (assetResponseList, patrolInspectionLogsMap) -> {
                    List<CompletableFuture<Void>> futures = new ArrayList<>();
                    // 生成bridgeId到最早的date的映射
                    Map<String, LocalDate> assetIdToDateMap = new HashMap<>();
                    dateToAssetIdsMap.forEach((date, assetIds) -> {
                        assetIds.forEach(assetId -> {
                            assetIdToDateMap.put(assetId, date);
                        });
                    });
                    assetResponseList.forEach(response -> {
                        String maintenanceSectionId = response.getMaintenanceSectionId();
                        PatrolInspectionLogs patrolInspectionLogs = patrolInspectionLogsMap.get(maintenanceSectionId);
                        if (patrolInspectionLogs != null) {
                            Date collectTime = patrolInspectionLogs.getCollectTime();
                            LocalDate collectDate = collectTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                            LocalDate date = assetIdToDateMap.get(response.getAssetId());
                            //收集时间在最早的生成时间之后
                            if (date != null && collectDate.isAfter(date)) {
                                futures.add(handleNonExistingAssetsAsync(
                                        response, patrolInspectionLogs,
                                        assetIdToDayFrequencyMap.get(response.getAssetId()),
                                        nowDate,
                                        InspectionType.fromAssetType(assetType, flag),
                                        userName,
                                        nonExistingAssetQueue));
                            }
                        }
                    });
                    return futures;
                });
        CompletableFuture<Void> combinedFuture = existingAssetsFuture.thenCombine(
                noExistingAssetsFuture,
                (existingAssets, noExistingAssets) -> {
                    List<CompletableFuture<Void>> allFutures = new ArrayList<>();
                    allFutures.addAll(existingAssets);
                    allFutures.addAll(noExistingAssets);
                    return CompletableFuture.allOf(allFutures.toArray(new CompletableFuture[0]));
                }
        ).thenCompose(f -> f);

        combinedFuture.thenRun(() -> {
            existingAssetQueue.add(END_MARKER_DETAIL);
            nonExistingAssetQueue.add(END_MARKER);
        });

        // 等待处理线程完成
        CompletableFuture.allOf(existingAssetsProcessing, nonExistingAssetsProcessing).join();
        if (updateAssetIds.isEmpty()) {
            log.info("无更新数据");
            return;
        }
        Integer updateNum = patrolInspectionLastService.updateGeneratedAtByAssetIdList(
                new ArrayList<>(updateAssetIds), assetType, nowDate.atTime(LocalTime.of(23, 59, 59)), null);
        log.info(String.format("更新generatedAt数量：%d", updateNum));
    }

    @Async("taskExecutor")
    public CompletableFuture<Void> handleExistingAssetsAsync(
            PatrolAssetCheck patrolAssetCheck,
            BlockingQueue<List<PatrolAssetCheckDetail>> existingAssetQueue,
            PatrolInspectionLogs log,
            String userName) {
        List<PatrolAssetCheckDetail> details = handleExistingAssets(patrolAssetCheck, log.getId(), userName);
        existingAssetQueue.add(details);
        return CompletableFuture.completedFuture(null);
    }

    @Async("taskExecutor")
    public CompletableFuture<Void> handleNonExistingAssetsAsync(
            BaseDataDomain assetResponse,
            PatrolInspectionLogs patrolInspectionLogs,
            Integer frequency,
            LocalDate nowDate,
            InspectionType inspectionType,
            String userName,
            BlockingQueue<PatrolAssetCheck> nonExistingAssetQueue) {
        PatrolAssetCheck po = handleNonExistingAssets(assetResponse, patrolInspectionLogs, frequency, nowDate, inspectionType, userName);
        nonExistingAssetQueue.add(po);
        return CompletableFuture.completedFuture(null);
    }

    // 处理存在的资产ID的逻辑
    private List<PatrolAssetCheckDetail> handleExistingAssets(
            PatrolAssetCheck patrolAssetCheck,
            String logId,
            String userName) {
        return patrolAssetCheckDetailService.generatePatrolAssetCheckDetailList(
                patrolAssetCheck, logId, userName);
    }

    /**
     * 处理不存在的资产ID的逻辑
     *
     * @param assetResponse 资产信息
     * @param nowDate       时间只生成一
     */
    private PatrolAssetCheck handleNonExistingAssets(BaseDataDomain assetResponse,
                                                     PatrolInspectionLogs patrolInspectionLogs,
                                                     Integer frequency,
                                                     LocalDate nowDate,
                                                     InspectionType inspectionType,
                                                     String userName) {
        SysDept dept = remoteDeptAuthService.getBranchOfficeByDeptId(
                Long.valueOf(patrolInspectionLogs.getMaintenanceUnitId())).getData();
        if(dept!=null)
        {
            patrolInspectionLogs.setMaintenanceUnitId(dept.getDeptId().toString());
            patrolInspectionLogs.setMaintenanceUnitName(dept.getDeptName());
        }
        PatrolAssetCheck patrolAssetCheck = new PatrolAssetCheck(
                assetResponse, patrolInspectionLogs, frequency, nowDate, inspectionType, userName);
        List<PatrolAssetCheckDetail> patrolAssetCheckDetailList =
                patrolAssetCheckDetailService.generatePatrolAssetCheckDetailList(
                        patrolAssetCheck, patrolInspectionLogs.getId(), userName);
        patrolAssetCheck.setPatrolCheckDetailList(patrolAssetCheckDetailList);
        return patrolAssetCheck;
    }
    //endregion


    //region 经常检查生成部分

    /**
     * 传入查询条件PatrolAssetCheckRequest，查询PatrolAssetCheckDTO
     */
//    @Transactional
    public void scheduleGenerateCheck(YearMonth nowDate, AssetType assetType) {
        if (assetType == null) {
            throw new IllegalArgumentException("assetType不能为空");
        }
        // 资产ID到月频率的映射
        Map<String, Integer> assetIdToDayFrequencyMap = new HashMap<>();
        // 上次生成到资产ID列表的映射
        Map<YearMonth, Map<YearMonth, List<String>>> dateToAssetIdsMap =
                patrolInspectionLastService.generatedAtByAssetIdList(nowDate, assetType, assetIdToDayFrequencyMap);
        // 使用 TreeMap 对 dateToAssetIdsMap 进行排序
        TreeMap<YearMonth, Map<YearMonth, List<String>>> sortedDateToAssetIdsMap = new TreeMap<>(dateToAssetIdsMap);
        // 遍历排序后的 Map 并调用 autoGenerateCheck
        sortedDateToAssetIdsMap.forEach((date, assetIdsMap) -> {
            autoGenerateCheck(date, assetType, assetIdsMap, assetIdToDayFrequencyMap);
        });
//        for (Map.Entry<YearMonth, Map<YearMonth, List<String>>> entry : sortedDateToAssetIdsMap.entrySet()) {
//            YearMonth key = entry.getKey();
//            Map<YearMonth, List<String>> value = entry.getValue();
//            autoGenerateCheck(key, assetType, value, assetIdToDayFrequencyMap);
//        }
    }

    //    @Transactional
    public void autoGenerateCheck(YearMonth nowDate, AssetType assetType, Map<YearMonth, List<String>> dateToAssetIdsMap, Map<String, Integer> assetIdToFrequencyMap) {
        final boolean flag = false;

        Set<String> updateAssetIds = ConcurrentHashMap.newKeySet();

        // 使用阻塞队列和CompletableFuture处理资产
        final BlockingQueue<PatrolAssetCheck> assetQueue = new LinkedBlockingQueue<>();

        String processId = UUID.randomUUID().toString();
        CompletableFuture<Void> assetsProcessing = CompletableFuture.runAsync(() -> processNonExistingAssets(
                assetQueue, updateAssetIds, assetType, flag, processId));

//        // 使用CompletableFuture并行处理频率映射和资产响应列表
//        CompletableFuture<Map<String, Integer>> assetIdFrequencyMapFuture = CompletableFuture.supplyAsync(() -> {
//            if (assetIdToFrequencyMap == null || assetIdToFrequencyMap.isEmpty()) {
//                return patrolFrequencySettingsService.assetMapByType(assetType, flag);
//            } else {
//                return assetIdToFrequencyMap;
//            }
//        });

        CompletableFuture<List<BaseDataDomain>> assetResponseListFuture = CompletableFuture.supplyAsync(() -> {
            List<String> generateAssetIds = dateToAssetIdsMap.values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            AssetBaseDataRequest assetBaseDataRequest = new AssetBaseDataRequest();
            assetBaseDataRequest.setIds(generateAssetIds);
            assetBaseDataRequest.setAssetType(assetType);
            assetBaseDataRequest.setDataRule(false);
            List<?> assetResponseList = baseCacheService.selectBaseDataResponseByAssetIds(assetBaseDataRequest);
            List<BaseDataDomain> baseDataDomainList = new ArrayList<>();
            for (Object o : assetResponseList) {
                BaseDataDomain baseDataDomain = new BaseDataDomain();
                BeanUtils.copyProperties(o, baseDataDomain);
                baseDataDomainList.add(baseDataDomain);
            }
            return baseDataDomainList;
        });

        CompletableFuture<List<CompletableFuture<Void>>> assetFuture = assetResponseListFuture.thenApply(assetResponseList ->
                assetResponseList.stream()
                        .map(response -> {
                            Integer frequency = assetIdToFrequencyMap.get(response.getAssetId());
                            return handleAssetsAsync(response, frequency,
                                    nowDate,
                                    InspectionType.fromAssetType(assetType, flag), assetQueue);
                        })
                        .collect(Collectors.toList()));


        List<CompletableFuture<Void>> futures = assetFuture.join();


        // 等待所有handle操作完成后，向队列添加null标记
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).thenRun(() -> assetQueue.add(END_MARKER));

        // 等待处理线程完成
        assetsProcessing.join();
        if (updateAssetIds.isEmpty()) {
            log.info("无更新数据");
            return;
        }
        Integer updateNum = patrolInspectionLastService.updateGeneratedAtByAssetIdList(
                new ArrayList<>(updateAssetIds), assetType, null, nowDate.atDay(1).atStartOfDay());
        log.info(String.format("更新generatedAt数量：%d", updateNum));
    }


    @Async("taskExecutor")
    public CompletableFuture<Void> handleAssetsAsync(
            BaseDataDomain assetResponse,
            Integer frequency,
            YearMonth nowYearMonth,
            InspectionType inspectionType,
            BlockingQueue<PatrolAssetCheck> nonExistingAssetQueue) {
        PatrolAssetCheck po = handleAssets(assetResponse, frequency, nowYearMonth, inspectionType);
        nonExistingAssetQueue.add(po);
        return CompletableFuture.completedFuture(null);
    }


    /**
     * 处理不存在的资产ID的逻辑
     *
     * @param assetResponse  资产信息
     * @param frequency      频率
     * @param nowYearMonth   时间只生成一次
     * @param inspectionType 检查类型
     * @return PatrolAssetCheck 生成的PatrolAssetCheck
     */
    private PatrolAssetCheck handleAssets(BaseDataDomain assetResponse,
                                          Integer frequency,
                                          YearMonth nowYearMonth,
                                          InspectionType inspectionType) {
        PatrolAssetCheck patrolAssetCheck = new PatrolAssetCheck(assetResponse, frequency, nowYearMonth, inspectionType);
        List<PatrolAssetCheckDetail> patrolAssetCheckDetailList =
                patrolAssetCheckDetailService.generatePatrolAssetCheckDetailList(
                        InspectionType.BRIDGE_DAILY_INSPECTION, patrolAssetCheck.getId());
        patrolAssetCheck.setPatrolCheckDetailList(patrolAssetCheckDetailList);
        return patrolAssetCheck;
    }

    //endregion


    /**
     * 获取date需要自动生成的桥梁ID列表
     *
     * @param date 传入日期，避免极端情况跨日期
     * @return 日期到桥梁ID列表的映射
     */
    private Map<LocalDate, Map<LocalDate, List<String>>> generaByAssetIdList(
            LocalDate date, InspectionType type, List<String> ids, Map<String, Integer> assetIdToFrequencyMap) {

        if (CollectionUtil.isEmpty(ids)) {
            return new HashMap<>();
        }

        // 获取频率映射
        Map<String, Integer> frequencyMap = patrolFrequencySettingsService.assetMapByType(type.getAssetType(), true);

        // 如果只有一个ID，使用单条查询
        if (ids.size() == 1) {
            String id = ids.get(0);
            PatrolAssetCheck check = new PatrolAssetCheck();
            check.setAssetId(id);
            check.setType(type);
            Integer frequency = FrequencyUtils.getFrequency(frequencyMap.get(id), type.getAssetType());
            check.setFrequency(frequency);
            check.setCheckTime(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()));

            LocalDateTime nextExpiry = findExpiryResults(check);
            if (nextExpiry == null) {
                return new HashMap<>();
            }

            assetIdToFrequencyMap.put(id, frequency);
            LocalDateTime lastExpiry = type.getFlag() ?
                    nextExpiry.minusDays(frequency) :
                    YearMonth.from(nextExpiry.minusMonths(frequency)).atEndOfMonth().atTime(LocalTime.MAX);

            Map<LocalDate, List<String>> innerMap = new HashMap<>();
            innerMap.put(lastExpiry.toLocalDate(), Collections.singletonList(id));

            return Collections.singletonMap(nextExpiry.toLocalDate(), innerMap);
        }

        // 批量查询逻辑保持不变
        List<PatrolAssetCheck> checks = ids.stream()
                .map(id -> {
                    PatrolAssetCheck check = new PatrolAssetCheck();
                    check.setAssetId(id);
                    check.setType(type);
                    Integer frequency = FrequencyUtils.getFrequency(frequencyMap.get(id), type.getAssetType());
                    check.setFrequency(frequency);
                    check.setCheckTime(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                    return check;
                })
                .collect(Collectors.toList());

        Map<String, LocalDateTime> expiryMap = findExpiryResultsBatch(checks);

        return checks.parallelStream()
                .map(check -> {
                    String id = check.getAssetId();
                    LocalDateTime nextExpiry = expiryMap.get(id);
                    if (nextExpiry == null) {
                        return null;
                    }

                    Integer frequency = check.getFrequency();
                    assetIdToFrequencyMap.put(id, frequency);

                    LocalDateTime lastExpiry = type.getFlag() ?
                            nextExpiry.minusDays(frequency) :
                            YearMonth.from(nextExpiry.minusMonths(frequency)).atEndOfMonth().atTime(LocalTime.MAX);

                    return new AbstractMap.SimpleEntry<>(
                            nextExpiry.toLocalDate(),
                            new AbstractMap.SimpleEntry<>(lastExpiry.toLocalDate(), id)
                    );
                })
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                        AbstractMap.SimpleEntry::getKey,
                        ConcurrentHashMap::new,
                        Collectors.groupingBy(
                                entry -> entry.getValue().getKey(),
                                ConcurrentHashMap::new,
                                Collectors.mapping(entry -> entry.getValue().getValue(), Collectors.toList())
                        )
                ));
    }

    // region 巡查日期获取

    /**
     * 根据assetId和yearMonth筛选并合并日期区间
     *
     * @param assetId   资产ID
     * @param yearMonth 年月（格式：YYYY-MM）
     * @return 合并后的日期列表
     */
    @Override
    public List<Integer> getMergedDays(InspectionType type, String assetId, String yearMonth) {
        // 处理输入的yearMonth，去除首尾空格
        yearMonth = yearMonth.trim();

        // 使用DateTimeFormatter解析日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        YearMonth ym;
        try {
            ym = YearMonth.parse(yearMonth, formatter);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("年月格式不正确，应为 yyyy-MM 格式，例如：2024-10");
        }

        // 获取数据库中符合条件的记录
        List<PatrolAssetCheck> checks = patrolAssetCheckMapper.findByAssetIdAndYearMonth(assetId, yearMonth, type, type.getAssetType().getTableName());

        List<int[]> intervals = new ArrayList<>();

        // 提取日期区间
        for (PatrolAssetCheck check : checks) {
            LocalDateTime expiryDate = check.getExpiry();
            // 如果是null或者0 则默认是1
            int frequency = check.getFrequency() == null || check.getFrequency() == 0 ? 1 : check.getFrequency();
            LocalDate startDate = LocalDate.from(expiryDate.minusDays(frequency).plusDays(1));

            // 仅处理与指定yearMonth相关的日期
            if (startDate.getYear() == ym.getYear() && startDate.getMonthValue() == ym.getMonthValue()) {
                intervals.add(new int[]{startDate.getDayOfMonth(), expiryDate.getDayOfMonth()});
            }
        }

        // 合并重叠的区间
        List<int[]> merged = mergeIntervals(intervals);

        // 收集所有天数
        List<Integer> days = new ArrayList<>();
        for (int[] interval : merged) {
            for (int day = interval[0]; day <= interval[1]; day++) {
                days.add(day);
            }
        }

        // 去重并排序
        return days.stream().distinct().sorted().collect(Collectors.toList());
    }

    /**
     * 合并重叠的日期区间
     *
     * @param intervals 日期区间列表
     * @return 合并后的日期区间列表
     */
    private List<int[]> mergeIntervals(List<int[]> intervals) {
        List<int[]> merged = new ArrayList<>();
        if (intervals.isEmpty()) {
            return merged;
        }

        // 按起始天数排序
        intervals.sort((a, b) -> Integer.compare(a[0], b[0]));

        int[] current = intervals.get(0);

        for (int i = 1; i < intervals.size(); i++) {
            int[] next = intervals.get(i);
            if (next[0] <= current[1] + 1) { // 有重叠或相邻
                current[1] = Math.max(current[1], next[1]);
            } else {
                merged.add(current);
                current = next;
            }
        }
        merged.add(current);

        return merged;
    }
    // endregion


    /**
     * 查看当前用户的权限id
     *
     * @param assetType 资产类型
     * @return List<String> 权限id列表
     */
    public List<String> getPermissionIds(AssetType assetType) {
        switch (assetType) {
            case BRIDGE:
                R<List<String>> bridgeIds = remoteDeptAuthService.findUserMaintenanceIds(null);
                if (bridgeIds.getCode() != 200) {
                    throw new RuntimeException("获取桥梁权限失败");
                }
                return bridgeIds.getData();
            case CULVERT:
                R<List<String>> culvertIds = remoteDeptAuthService.findUserCulvertIds(null);
                if (culvertIds.getCode() != 200) {
                    throw new RuntimeException("获取涵洞权限失败");
                }
                return culvertIds.getData();
            case TUNNEL:
                R<List<String>> tunnelIds = remoteDeptAuthService.findUserTunnelIds(null);
                if (tunnelIds.getCode() != 200) {
                    throw new RuntimeException("获取隧道权限失败");
                }
                return tunnelIds.getData();
            default:
                return Collections.emptyList();
        }
    }

    @Override
    public List<PatrolAssetCheck> selectByCreateTimeAndDiseaseCount(LocalDateTime createTime, String tableName) {
        DynamicDataSourceContextHolder.push("slave");
        List<PatrolAssetCheck> list = baseMapper.selectByCreateTimeAndDiseaseCount(createTime, tableName);
        DynamicDataSourceContextHolder.clear();
        return list;

    }

    /**
     * 根据指定的创建时间更新病害数量
     * 处理桥梁、隧道和涵洞三种资产类型
     * 每100条记录批量更新一次
     */
    @Override
    public void updateDiseaseCountByCreateTime() {
        log.info("开始执行指定时间的病害数量更新任务");

        // 指定的创建时间
        LocalDateTime createTime = LocalDateTime.of(2025, 1, 13, 0, 0, 0);

        // 处理桥梁资产
        processAssetTypeDisease(createTime, AssetType.BRIDGE);

        // 处理隧道资产
        processAssetTypeDisease(createTime, AssetType.TUNNEL);

        // 处理涵洞资产
        processAssetTypeDisease(createTime, AssetType.CULVERT);

        log.info("指定时间的病害数量更新任务完成");
    }

    /**
     * 处理指定资产类型的病害数量更新
     *
     * @param createTime 创建时间
     * @param assetType  资产类型
     */
    private void processAssetTypeDisease(LocalDateTime createTime, AssetType assetType) {
        String tableName = assetType.getTableName();
        log.info("开始处理{}资产的病害数量更新", assetType.getDescription());

        // 查询指定创建时间之后的记录
        List<PatrolAssetCheck> patrolAssetChecks = selectByCreateTimeAndDiseaseCount(createTime, tableName);
        log.info("查询到{}条{}资产记录", patrolAssetChecks.size(), assetType.getDescription());

        if (patrolAssetChecks.isEmpty()) {
            log.info("{}资产没有需要更新的记录", assetType.getDescription());
            return;
        }

        // 批量处理记录
        List<PatrolDiseaseDTO> batch = new ArrayList<>();
        int totalUpdated = 0;

        for (PatrolAssetCheck check : patrolAssetChecks) {
            // 计算病害数量
            setDiseaseNum(check);

            // 创建病害DTO对象
            PatrolDiseaseDTO diseaseDTO = new PatrolDiseaseDTO(check.getId(), check.getDiseaseCount());
            batch.add(diseaseDTO);

            // 每100条记录更新一次
            if (batch.size() >= 100) {
                updateDiseaseNumbers(batch, tableName);
                log.info("已更新{}条{}资产的病害数量", batch.size(), assetType.getDescription());
                totalUpdated += batch.size();
                batch.clear();
            }
        }

        // 处理剩余的记录
        if (!batch.isEmpty()) {
            updateDiseaseNumbers(batch, tableName);
            log.info("已更新剩余{}条{}资产的病害数量", batch.size(), assetType.getDescription());
            totalUpdated += batch.size();
        }

        log.info("{}资产病害数量更新完成，共更新{}条记录", assetType.getDescription(), totalUpdated);
    }

    /**
     * 根据ID列表批量更新状态
     *
     * @param request 包含checkIds和status的请求对象
     * @return 更新的记录数
     */
    @Override
    public int updateStatusByIds(AssetBaseDataRequest request) {
        // 参数校验
        if (request == null || CollectionUtil.isEmpty(request.getCheckIds()) || request.getStatus() == null || request.getAssetType() == null) {
            log.error("更新状态参数无效: checkIds为空或status为null或assetType为null");
            return 0;
        }

        // 创建更新实体
        PatrolAssetCheck entity = new PatrolAssetCheck();

        // 设置审核时间
        if (request.getAuditTime() == null) {
            entity.setAuditTime(new Date());
        } else {
            // 将LocalDateTime转换为Date
            entity.setAuditTime(Date.from(request.getAuditTime().atZone(ZoneId.systemDefault()).toInstant()));
        }

        // 设置审核状态
        entity.setStatus(request.getStatus());

        // 设置审核人信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        entity.setKahunaId(loginUser.getUserid() + "");
        entity.setKahunaName(loginUser.getSysUser().getNickName());
        entity.setKahunaSign(loginUser.getSysUser().getSignId());

        // 获取表名
        String tableName = request.getAssetType().getTableName();
        if (StringUtils.isBlank(tableName)) {
            log.error("获取表名失败");
            return 0;
        }

        try {
            // 切换到从库
            DynamicDataSourceContextHolder.push("slave");
            // 执行批量更新，传递实体对象
            int updatedCount = patrolAssetCheckMapper.updateStatusByIds(request.getCheckIds(), entity, tableName);
            log.info("成功更新{}条记录的状态为{}", updatedCount, request.getStatus());
            return updatedCount;
        } catch (Exception e) {
            log.error("更新状态时发生错误", e);
            throw e;
        } finally {
            // 清理数据源上下文
            DynamicDataSourceContextHolder.clear();
        }
    }
    public void generateHistory(AssetBaseDataRequest request) {
        String assetId = request.getAssetId();
        if (StringUtils.isBlank(assetId) && CollectionUtil.isNotEmpty(request.getIds())) {
            assetId = request.getIds().get(0);
        }
        InspectionType inspectionType = request.getType();
        LocalDateTime checkTimeStart = request.getCheckStartTime();
        LocalDateTime checkTimeEnd = request.getCheckEndTime();

        if (StringUtils.isBlank(assetId) || inspectionType == null || checkTimeStart == null || checkTimeEnd == null) {
            log.error("generateHistory-参数不足，无法生成历史数据。 request: {}", request);
            return;
        }

        log.info("开始为资产 {} 生成历史巡检数据，时间范围: {} 到 {}", assetId, checkTimeStart, checkTimeEnd);
        AssetType assetType = inspectionType.getAssetType();
        String tableName = assetType.getTableName();

        // 查询指定时间范围内的数据
        AssetBaseDataRequest duringLocalDate = new AssetBaseDataRequest();
        duringLocalDate.setAssetType(assetType);
        duringLocalDate.setType(inspectionType);
        duringLocalDate.setIds(Collections.singletonList(assetId));
        duringLocalDate.setCheckStartTime(checkTimeStart);
        duringLocalDate.setCheckEndTime(checkTimeEnd);
        List<PatrolAssetCheck> patrolLocalDate = this.selectPatrolAssetCheck(duringLocalDate, null, null, new AtomicInteger());

        // 1. 查询模板数据
        List<PatrolAssetCheck> templates = new ArrayList<>();

        String customSqlCondition = "opr_user_id IS NOT NULL AND opr_user_id != '' AND route_code IS NOT NULL AND route_code != '' AND property_unit_id IS NOT NULL AND property_unit_id != '' AND property_unit_name IS NOT NULL AND property_unit_name != '' AND maintenance_section_id IS NOT NULL AND maintenance_section_id != '' AND maintain_unit_id IS NOT NULL AND maintain_unit_id != '' AND maintain_unit_name IS NOT NULL AND maintain_unit_name != ''";
        // 查询指定时间范围内的数据
        AssetBaseDataRequest duringReq = new AssetBaseDataRequest();
        duringReq.setAssetType(assetType);
        duringReq.setType(inspectionType);
        duringReq.setIds(Collections.singletonList(assetId));
        duringReq.setCheckStartTime(checkTimeStart);
        duringReq.setCheckEndTime(checkTimeEnd);
        duringReq.setCustomSqlCondition(customSqlCondition);
        templates.addAll(this.selectPatrolAssetCheck(duringReq, null, null, new AtomicInteger()));

        // 查询之前100条
        AssetBaseDataRequest beforeReq = new AssetBaseDataRequest();
        beforeReq.setAssetType(assetType);
        beforeReq.setType(inspectionType);
        beforeReq.setIds(Collections.singletonList(assetId));
        beforeReq.setCheckEndTime(checkTimeStart);
        beforeReq.setCustomSqlCondition(customSqlCondition);
        beforeReq.setOrderBy("check_time");
        beforeReq.setOrderDirection("DESC");
        templates.addAll(this.selectPatrolAssetCheck(beforeReq, 1L, 100L, new AtomicInteger()));

        // 查询之后100条
        AssetBaseDataRequest afterReq = new AssetBaseDataRequest();
        afterReq.setAssetType(assetType);
        afterReq.setType(inspectionType);
        afterReq.setIds(Collections.singletonList(assetId));
        afterReq.setCheckStartTime(checkTimeEnd);
        afterReq.setCustomSqlCondition(customSqlCondition);
        afterReq.setOrderBy("check_time");
        afterReq.setOrderDirection("ASC");
        templates.addAll(this.selectPatrolAssetCheck(afterReq, 1L, 100L, new AtomicInteger()));

        if (templates.isEmpty()) {
            log.warn("未找到资产 {} 的模板数据，无法生成历史记录。", assetId);
            return;
        }

        // 去重
        templates = new ArrayList<>(templates.stream()
                .collect(Collectors.toMap(PatrolAssetCheck::getId, Function.identity(), (o1, o2) -> o1, LinkedHashMap::new))
                .values());
        log.info("找到 {} 条模板数据。", templates.size());

        // 2. 获取频率
        Integer frequency = patrolFrequencySettingsService.getFrequencyByAssetId(assetId, inspectionType.getAssetType().getCode(), inspectionType.getFlag());
        frequency = FrequencyUtils.getFrequency(frequency, inspectionType.getAssetType());

        if (frequency == null || frequency == 0) {
            log.warn("资产 {} 未配置巡检频率，无法生成历史记录。", assetId);
            return;
        }

        // 准备详情模板
        List<PatrolPartsInfo> partsInfoList = patrolPartsInfoService.selectByType(inspectionType);
        if (CollectionUtil.isEmpty(partsInfoList)) {
            log.warn("未找到资产类型 {} 的巡检部件配置，将不生成巡检详情。", inspectionType.getDescription());
        }

        List<PatrolAssetCheckDetail> detailTemplates = partsInfoList.stream()
                .map(partsInfo -> {
                    PatrolAssetCheckDetail detail = new PatrolAssetCheckDetail();
                    detail.setId(IdWorker.getIdStr());
                    detail.setPartsTypeId(partsInfo.getId());
                    detail.setPartsTypeName(partsInfo.getPartsName());
                    detail.setDefect("未见异常"); // 正常状态
                    detail.setCreateBy("system-history-generator");
                    detail.setUpdateBy("system-history-generator");
                    detail.setCreateTime(LocalDateTime.now());
                    detail.setUpdateTime(LocalDateTime.now());
                    return detail;
                }).collect(Collectors.toList());

        // 收集所有已存在的巡检日期，以避免重复
        Set<LocalDate> existingCheckDates = patrolLocalDate.stream()
                .map(PatrolAssetCheck::getCheckTime)
                .filter(Objects::nonNull)
                .map(date -> date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())
                .collect(Collectors.toSet());

        // 3. 循环生成数据
        List<PatrolAssetCheck> newChecks = new ArrayList<>();
        Random random = new Random();
        LocalDateTime currentCheckTime = checkTimeStart;

        while (!currentCheckTime.isAfter(checkTimeEnd)) {
            // 如果当前日期已经存在记录，则跳过
            if (existingCheckDates.contains(currentCheckTime.toLocalDate())) {
                log.info("资产 {} 在 {} 已有巡检记录，跳过生成。", assetId, currentCheckTime.toLocalDate());
                // 增加时间
                if (inspectionType.getFlag()) {
                    currentCheckTime = currentCheckTime.plusDays(frequency);
                } else {
                    currentCheckTime = currentCheckTime.plusMonths(frequency);
                }
                continue;
            }
            // 随机选择一个模板
            PatrolAssetCheck template = templates.get(random.nextInt(templates.size()));
            PatrolAssetCheck newCheck = new PatrolAssetCheck();
            BeanUtils.copyProperties(template, newCheck);

            // 设置新属性
            newCheck.setId(IdWorker.getIdStr());
            newCheck.setCheckTime(Date.from(currentCheckTime.atZone(ZoneId.systemDefault()).toInstant()));
            newCheck.setFrequency(frequency);

            // 重新计算并设置到期时间
            setExpiryAndFrequency(newCheck);

            // 重置审核信息
            newCheck.setAuditTime(null);
            newCheck.setStatus(AuditStatusType.PENDING);
            newCheck.setKahunaId(null);
            newCheck.setKahunaName(null);
            newCheck.setKahunaSign(null);
            newCheck.setRemark(null);

            // 设置阶段
            newCheck.setStage(StageType.IN_PERIOD);

            // 更新创建和修改信息
            LocalDateTime now = LocalDateTime.now();
            newCheck.setCreateTime(now);
            newCheck.setUpdateTime(now);
            newCheck.setCreateBy("system-history-generator");
            newCheck.setUpdateBy("system-history-generator");

            // 附加正常的巡检详情
            if (CollectionUtil.isNotEmpty(detailTemplates)) {
                List<PatrolAssetCheckDetail> newDetails = new ArrayList<>();
                for (PatrolAssetCheckDetail detailTemplate : detailTemplates) {
                    PatrolAssetCheckDetail newDetail = new PatrolAssetCheckDetail();
                    BeanUtils.copyProperties(detailTemplate, newDetail);
                    newDetail.setId(IdWorker.getIdStr()); // 确保每个详情都有唯一ID
                    newDetail.setCheckId(newCheck.getId());
                    newDetails.add(newDetail);
                }
                newCheck.setPatrolCheckDetailList(newDetails);
            }

            newChecks.add(newCheck);

            // 增加时间
            if (inspectionType.getFlag()) {
                currentCheckTime = currentCheckTime.plusDays(frequency);
            } else {
                currentCheckTime = currentCheckTime.plusMonths(frequency);
            }
        }

        // 4. 批量保存
        if (!newChecks.isEmpty()) {
            log.info("准备批量插入 {} 条新生成的巡检记录。", newChecks.size());
            saveAll(newChecks, tableName);

            List<PatrolAssetCheckDetail> allDetails = newChecks.stream()
                    .map(PatrolAssetCheck::getPatrolCheckDetailList)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(allDetails)) {
                log.info("准备批量插入 {} 条新生成的巡检详情记录。", allDetails.size());
                patrolAssetCheckDetailService.saveAll(allDetails, tableName);
            }
        }

        log.info("资产 {} 的历史巡检数据生成完成。", assetId);
    }
}
