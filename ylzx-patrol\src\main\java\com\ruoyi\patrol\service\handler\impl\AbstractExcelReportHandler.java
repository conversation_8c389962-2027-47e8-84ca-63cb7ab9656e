package com.ruoyi.patrol.service.handler.impl;

import cn.idev.excel.write.handler.SheetWriteHandler;
import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.util.CellRangeAddress;

import java.awt.Color;
import java.awt.font.FontRenderContext;
import java.awt.font.TextLayout;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.text.AttributedString;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;

/**
 * Excel报表处理器的抽象基类，提供样式创建、签名图像处理和实用方法等通用功能。
 *
 * @param <T> 报表行/部分的主要数据对象类型。
 */
@Slf4j
public abstract class AbstractExcelReportHandler<T> implements SheetWriteHandler {

    protected static final int SIGNATURE_HEIGHT_POINTS = 40; // 默认签名图像高度（以点为单位）
    protected static final float DEFAULT_MIN_ROW_HEIGHT = 18.0f; // 默认最小行高（以点为单位）

    // --- 通用样式定义 ---
    protected CellStyle titleStyle;
    protected CellStyle headerLabelStyle;
    protected CellStyle headerValueStyle;
    protected CellStyle itemHeaderStyle;
    protected CellStyle itemCellStyle; // 通用项目单元格样式（例如，居中）
    protected CellStyle itemFirstColStyle; // 第一列的样式（例如，左对齐）

    // --- 签名处理 ---
    protected final Map<String, String> signUrlMap = new ConcurrentHashMap<>(); // 每个签名ID对应的URL
    protected final Map<String, byte[]> signImageMap = new ConcurrentHashMap<>(); // 每个签名ID对应的图像数据
    protected final Set<String> failedSignIds = ConcurrentHashMap.newKeySet(); // 下载失败的ID集合

    // --- HttpClient实例 ---
    protected final HttpClient httpClient = HttpClient.newBuilder()
            .followRedirects(HttpClient.Redirect.NORMAL)
            .connectTimeout(Duration.ofSeconds(10))
            .build();

    // 用于缓存样式以避免超出Excel的样式限制(最大64000个样式)
    protected final Map<String, CellStyle> cellStyleCache = new HashMap<>();

    /**
     * 初始化通用样式。由beforeSheetCreate调用。
     * 子类应调用super.createStyles()，然后根据需要添加/修改样式。
     *
     * @param workbook 工作簿实例。
     */
    protected void createStyles(Workbook workbook) {
        // 标题样式
        titleStyle = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setWrapText(true);

        // 标题标签样式（默认：右对齐，粗体）
        headerLabelStyle = workbook.createCellStyle();
        Font headerLabelFont = workbook.createFont();
        headerLabelFont.setBold(true);
        headerLabelFont.setFontHeightInPoints((short) 10);
        headerLabelStyle.setFont(headerLabelFont);
        headerLabelStyle.setAlignment(HorizontalAlignment.CENTER);
        headerLabelStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerLabelStyle.setWrapText(true);

        // 标题值样式（默认：左对齐）
        headerValueStyle = workbook.createCellStyle();
        Font headerValueFont = workbook.createFont();
        headerValueFont.setFontHeightInPoints((short) 10);
        headerValueStyle.setFont(headerValueFont);
        headerValueStyle.setAlignment(HorizontalAlignment.LEFT);
        headerValueStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerValueStyle.setWrapText(true);

        // 项目标题样式（默认：居中，粗体）
        itemHeaderStyle = workbook.createCellStyle();
        Font itemHeaderFont = workbook.createFont();
        itemHeaderFont.setBold(true);
        itemHeaderFont.setFontHeightInPoints((short) 11);
        itemHeaderStyle.setFont(itemHeaderFont);
        itemHeaderStyle.setAlignment(HorizontalAlignment.CENTER);
        itemHeaderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        itemHeaderStyle.setWrapText(true);
        // setSolidBackground(itemHeaderStyle, new Color(217, 217, 217)); // 可选背景

        // 项目单元格样式（默认：居中）
        itemCellStyle = workbook.createCellStyle();
        itemCellStyle.setAlignment(HorizontalAlignment.CENTER);
        itemCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        itemCellStyle.setWrapText(true);
        Font itemFont = workbook.createFont();
        itemFont.setFontHeightInPoints((short) 10);
        itemCellStyle.setFont(itemFont);

        // 项目第一列样式（默认：左对齐）
        itemFirstColStyle = workbook.createCellStyle();
        itemFirstColStyle.cloneStyleFrom(itemCellStyle); // 继承字体、自动换行等
        itemFirstColStyle.setAlignment(HorizontalAlignment.LEFT); // 覆盖对齐方式
    }

    /**
     * 使用异步HttpClient预加载签名图像。
     */
    protected void preloadSignImages() {
        if (signUrlMap.isEmpty()) {
            log.info("没有提供用于预加载的签名URL。");
            return;
        }
        failedSignIds.clear();
        int initialUrlCount = signUrlMap.size();
        log.info("开始为 {} 个URL预加载签名图像...", initialUrlCount);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        final long MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB 大小限制
        for (var entry : signUrlMap.entrySet()) {
            String id = entry.getKey();
            String url = entry.getValue();
            if (url == null || url.isBlank()) {
                log.warn("为签名ID提供的URL无效或为空: {}", id);
                failedSignIds.add(id);
                continue;
            }
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .GET()
                    .timeout(Duration.ofSeconds(10))
                    .header("User-Agent", "Mozilla/5.0 (compatible; RuoyiExcelExporter/1.0)")
                    .header("Accept", "image/png,image/jpeg,image/gif,image/*;q=0.8,*/*;q=0.5")
                    .build();
            CompletableFuture<Void> future = httpClient
                    // 以字节数组作为响应体处理器
                    .sendAsync(request, HttpResponse.BodyHandlers.ofByteArray())
                    .thenApply(response -> {
                        if (response.statusCode() != 200) {
                            log.warn("图像下载失败。HTTP状态: {} URL: {}", response.statusCode(), url);
                            throw new RuntimeException("Non-OK HTTP状态");
                        }
                        Optional<String> contentType = response.headers().firstValue("Content-Type");
                        if (contentType.isEmpty() || !contentType.get().toLowerCase().startsWith("image/")) {
                            log.warn("下载的内容不是图像。内容类型: {} URL: {}", contentType.orElse("null"), url);
                            throw new RuntimeException("非图像内容");
                        }
                        // 判断响应头中是否提供了长度信息（可能未知）
                        long contentLength = response.headers()
                                .firstValueAsLong("Content-Length")
                                .orElse(-1L);
                        if (contentLength > MAX_IMAGE_SIZE) {
                            log.warn("图像大小({})超过限制(5MB)，URL: {}", contentLength, url);
                            throw new RuntimeException("图像大小超过限制");
                        }
                        return response.body();
                    })
                    .thenAccept(imageData -> {
                        if (imageData != null && imageData.length > 0) {
                            // 补充：对于未知长度情况，也要在此校验总字节数
                            if (imageData.length > MAX_IMAGE_SIZE) {
                                log.warn("下载的图像数据超过大小限制(5MB)，URL: {}", url);
                                failedSignIds.add(id);
                            } else {
                                signImageMap.put(id, imageData);
                                log.debug("成功预加载ID为 {} 的图像", id);
                            }
                        } else {
                            log.warn("图像下载返回空数据，URL: {}", url);
                            failedSignIds.add(id);
                        }
                    })
                    .exceptionally(ex -> {
                        log.warn("从URL下载签名图像失败: {} - 错误: {}", url, ex.getMessage());
                        log.debug("下载失败的完整异常:", ex);
                        failedSignIds.add(id);
                        return null;
                    });
            futures.add(future);
        }
        // 等待所有异步任务完成，最多等待60秒。超时后也不会无限期阻塞
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(600, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("签名图像预加载任务未在600秒内完全完成。", e);
        }
        log.info("签名图像预加载完成。成功: {}, 失败: {}。(初始URL数: {})",
                signImageMap.size(), failedSignIds.size(), initialUrlCount);
    }

    /**
     * 根据图像数据的魔术数字确定POI图片类型常量。
     *
     * @param imageData 图像的字节数组。
     * @return 适当的Workbook.PICTURE_TYPE_*常量。默认为PNG。
     */
    protected int determinePictureType(byte[] imageData) {
        if (imageData == null || imageData.length < 8) {
            log.warn("图像数据不足以确定类型，默认为PNG。");
            return Workbook.PICTURE_TYPE_PNG;
        }

        // PNG: 89 50 4E 47 0D 0A 1A 0A
        if ((imageData[0] & 0xFF) == 0x89 && (imageData[1] & 0xFF) == 0x50 &&
            (imageData[2] & 0xFF) == 0x4E && (imageData[3] & 0xFF) == 0x47 &&
            (imageData[4] & 0xFF) == 0x0D && (imageData[5] & 0xFF) == 0x0A &&
            (imageData[6] & 0xFF) == 0x1A && (imageData[7] & 0xFF) == 0x0A) {
            return Workbook.PICTURE_TYPE_PNG;
        }
        // JPEG: FF D8 FF
        if ((imageData[0] & 0xFF) == 0xFF && (imageData[1] & 0xFF) == 0xD8 && (imageData[2] & 0xFF) == 0xFF) {
            return Workbook.PICTURE_TYPE_JPEG;
        }
        // GIF: 47 49 46 38 (GIF87a或GIF89a)
        if ((imageData[0] & 0xFF) == 0x47 && (imageData[1] & 0xFF) == 0x49 &&
            (imageData[2] & 0xFF) == 0x46 && (imageData[3] & 0xFF) == 0x38) {
            // POI有时可能在内部将GIF映射到PNG，但PICTURE_TYPE_GIF存在
             // return Workbook.PICTURE_TYPE_GIF; // 如果可用且有效，请使用
             return Workbook.PICTURE_TYPE_PNG; // 如果GIF导致问题，则回退
        }
        // BMP: 42 4D
        if ((imageData[0] & 0xFF) == 0x42 && (imageData[1] & 0xFF) == 0x4D) {
            return Workbook.PICTURE_TYPE_DIB; // 设备无关位图
        }
        // TIFF: 49 49 2A 00 or 4D 4D 00 2A
        if (((imageData[0] & 0xFF) == 0x49 && (imageData[1] & 0xFF) == 0x49 && (imageData[2] & 0xFF) == 0x2A && (imageData[3] & 0xFF) == 0x00) ||
            ((imageData[0] & 0xFF) == 0x4D && (imageData[1] & 0xFF) == 0x4D && (imageData[2] & 0xFF) == 0x00 && (imageData[3] & 0xFF) == 0x2A)) {
             // POI可能在所有格式（HS/XS）中都不能直接很好地支持TIFF写入
             log.warn("检测到TIFF图像，支持可能有限。为POI默认使用PNG类型。");
             return Workbook.PICTURE_TYPE_PNG;
        }

        log.warn("无法从魔术数字确定图像类型，默认为PNG。");
        return Workbook.PICTURE_TYPE_PNG;
    }


    // --- SheetWriteHandler实现 ---

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        // 清空样式缓存，避免不同报表间的样式混用
        cellStyleCache.clear();
        
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        createStyles(workbook);
        // 尝试预加载SignatureId对应的图片（以节省导出时间）
        if (!signUrlMap.isEmpty()) {
            preloadSignImages();
        }
    }

    // afterSheetCreate是抽象的或留给子类完全实现，
    // 因为表格内容生成对每个报表都是特定的。
    // @Override
    // public abstract void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder);


    // --- 辅助方法 ---

    /**
     * 在给定行的指定列索引处创建单元格，设置其值和样式。
     * 通过克隆创建新的样式实例，以避免修改共享样式。
     *
     * @param row          要创建单元格的行。
     * @param columnIndex  新单元格的列索引。
     * @param value        单元格的字符串值。
     * @param style        要应用的CellStyle（将被克隆）。
     * @return 新创建的单元格。
     */
    protected Cell createCell(Row row, int columnIndex, String value, CellStyle style) {
        Cell cell = row.createCell(columnIndex);
        cell.setCellValue(value != null ? value : ""); // 避免在setCellValue上出现空指针
        if (style != null) {
            // 直接使用提供的样式而不是每次都创建新样式
            cell.setCellStyle(style);
        }
        return cell;
    }

    /**
     * 将参考样式中的边框属性应用于行中给定范围内的所有单元格。
     * 如果单元格不存在，则创建单元格。确保现有单元格样式得到保留，只更新边框。
     *
     * @param row            要应用边框的行。
     * @param startCol       起始列索引（包含）。
     * @param endCol         结束列索引（包含）。
     * @param referenceStyle 从中复制边框属性的样式。
     */
    protected void applyRowBorder(Row row, int startCol, int endCol, CellStyle referenceStyle) {
        Workbook wb = row.getSheet().getWorkbook();
        for (int i = startCol; i <= endCol; i++) {
            Cell cell = row.getCell(i);
            if (cell == null) {
                cell = row.createCell(i);
            }
            // 获取现有样式
            CellStyle cellStyle = cell.getCellStyle();
            
            // 生成缓存键并尝试从缓存获取样式
            String cacheKey = generateStyleCacheKey(cellStyle, referenceStyle);
            CellStyle newCellStyle = cellStyleCache.get(cacheKey);
            
            // 如果缓存中没有找到样式，则创建新样式并放入缓存
            if (newCellStyle == null) {
                newCellStyle = wb.createCellStyle();
                if (cellStyle != null) {
                    newCellStyle.cloneStyleFrom(cellStyle);
                }
                // 从参考样式复制边框属性
                copyBorders(referenceStyle, newCellStyle);
                // 将新样式添加到缓存
                cellStyleCache.put(cacheKey, newCellStyle);
            }
            
            cell.setCellStyle(newCellStyle);
        }
    }

    /**
     * 仅从源CellStyle复制边框属性（样式和颜色）到目标CellStyle。
     *
     * @param source 从中复制边框的样式。
     * @param target 复制边框到的样式。
     */
    protected void copyBorders(CellStyle source, CellStyle target) {
        target.setBorderTop(source.getBorderTop());
        target.setTopBorderColor(source.getTopBorderColor());

        target.setBorderBottom(source.getBorderBottom());
        target.setBottomBorderColor(source.getBottomBorderColor());

        target.setBorderLeft(source.getBorderLeft());
        target.setLeftBorderColor(source.getLeftBorderColor());

        target.setBorderRight(source.getBorderRight());
        target.setRightBorderColor(source.getRightBorderColor());
    }

    /**
     * 为边框样式生成缓存键
     * 
     * @param originalStyle 原始样式（可以为null）
     * @param borderStyle 要应用的边框样式
     * @return 用于缓存的键
     */
    protected String generateStyleCacheKey(CellStyle originalStyle, CellStyle borderStyle) {
        StringBuilder key = new StringBuilder();
        
        // 添加原始样式的属性
        if (originalStyle != null) {
            key.append("align:").append(originalStyle.getAlignment())
               .append(";valign:").append(originalStyle.getVerticalAlignment())
               .append(";wrap:").append(originalStyle.getWrapText())
               .append(";rot:").append(originalStyle.getRotation())
               .append(";ind:").append(originalStyle.getIndention())
               .append(";font:").append(originalStyle.getFontIndex());
            
            // 如果有背景色
            key.append(";fill:").append(originalStyle.getFillPattern())
               .append(";fgColor:").append(originalStyle.getFillForegroundColor())
               .append(";bgColor:").append(originalStyle.getFillBackgroundColor());
        } else {
            key.append("orig:null");
        }
        
        // 添加边框样式的属性
        key.append(";border:").append(borderStyle.getBorderTop())
           .append(",").append(borderStyle.getBorderRight())
           .append(",").append(borderStyle.getBorderBottom())
           .append(",").append(borderStyle.getBorderLeft())
           .append(";borderColor:").append(borderStyle.getTopBorderColor())
           .append(",").append(borderStyle.getRightBorderColor())
           .append(",").append(borderStyle.getBottomBorderColor())
           .append(",").append(borderStyle.getLeftBorderColor());
        
        return key.toString();
    }

    /**
     * 将参考样式中的边框属性直接应用于特定单元格。
     * 对于确保合并单元格或需要特定边框处理的单元格的边框很有用。
     *
     * @param cell                     要应用边框的单元格。
     * @param styleToApplyBordersFrom 包含所需边框设置的样式。
     */
    protected void applyCellBorder(Cell cell, CellStyle styleToApplyBordersFrom) {
        if (cell == null || styleToApplyBordersFrom == null) return;

        CellStyle cellStyle = cell.getCellStyle();
        
        // 生成缓存键并尝试从缓存获取样式
        String cacheKey = generateStyleCacheKey(cellStyle, styleToApplyBordersFrom);
        Workbook wb = cell.getSheet().getWorkbook();
        CellStyle newCellStyle = cellStyleCache.get(cacheKey);
        
        // 如果缓存中没有找到样式，则创建新样式并放入缓存
        if (newCellStyle == null) {
            newCellStyle = wb.createCellStyle();
            if (cellStyle != null) {
                newCellStyle.cloneStyleFrom(cellStyle); // 保留现有设置
            }
            copyBorders(styleToApplyBordersFrom, newCellStyle); // 应用边框
            // 将新样式添加到缓存
            cellStyleCache.put(cacheKey, newCellStyle);
        }
        
        cell.setCellStyle(newCellStyle);
    }

    /**
     * 在给定的CellStyle上设置细黑边框（上、下、左、右）。
     *
     * @param style 要修改的CellStyle。
     */
    protected void setThinBorders(CellStyle style) {
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
    }

    /**
     * 为单元格样式设置实心背景颜色。适用于XSSF（.xlsx）。
     *
     * @param style 要修改的CellStyle（必须是XSSFCellStyle）。
     * @param color 用于背景的AWT颜色。
     */
    protected void setSolidBackground(CellStyle style, Color color) {
        if (style instanceof XSSFCellStyle xssfStyle) {
            // 创建XSSFColor。为IndexedColorMap传递null - POI将处理它。
            XSSFColor xssfColor = new XSSFColor(color, null);
            xssfStyle.setFillForegroundColor(xssfColor);
            xssfStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        } else {
            // HSSF（.xls）需要找到最接近的索引颜色
            // short paletteIndex = HSSFColor.getIndexHash().get(color.getRGB());
            // if (paletteIndex != 0) { // 0通常是黑色或无效
            //     style.setFillForegroundColor(paletteIndex);
            //     style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            // }
            log.warn("设置实心背景颜色主要支持XSSF（.xlsx）格式。");
        }
    }

    /**
     * 格式化桥梁和道路位置的桩号。
     * 如果可用，优先使用 preFormattedStake；否则对 BigDecimal 值做四舍五入到3位小数，
     * 再转换成字符串"Kxxx+yyy(.zz...)"的形式。
     * 
     * @param stakeValue 桩号值
     * @param preFormattedStake 预格式化桩号值
     * @return 格式化后的桩号字符串
     */
    public static String formatStake(BigDecimal stakeValue, String preFormattedStake) {
        // 1. 预格式化优先
        if (preFormattedStake != null && !preFormattedStake.isBlank()) {
            return preFormattedStake;
        }
        if (stakeValue == null) {
            return "";
        }
        // 2. 四舍五入到3位小数
        BigDecimal scaled = stakeValue.setScale(3, RoundingMode.HALF_UP);
        // 3. 拿到 unscaledValue，这个值相当于原数 * 10^3 的整数形式
        BigInteger unscaled = scaled.unscaledValue();
        boolean negative = unscaled.signum() < 0;
        BigInteger absUnscaled = unscaled.abs();
        // 4. 将它转成十进制字符串
        //    例如：原值 1234.568 -> scaled=1234.568 -> unscaled=1234568 -> digits="1234568"
        String digits = absUnscaled.toString();
        int len = digits.length();
        // 5. 拆出整数部分（米）和小数部分（千分米）
        //    len>3 时，前 len-3位 为 整数米；后3位 为 小数部分
        String intPart, fracPart;
        if (len > 3) {
            intPart  = digits.substring(0, len - 3);
            fracPart = digits.substring(len - 3);
        } else {
            // len<=3: 整数米为 0，小数部分要左补0到3位
            intPart  = "0";
            // 先拼 "000"+"XXX"，然后取最后三位
            fracPart = ("000" + digits).substring(digits.length());
        }
        // 6. 在整数米里再拆出"公里"和"余下的米"
        //    intPart 可能多于3位，左边是公里，右边是米
        int iLen = intPart.length();
        String kmPart, mIntPart;
        if (iLen > 3) {
            kmPart   = intPart.substring(0, iLen - 3);
            mIntPart = intPart.substring(iLen - 3);
        } else {
            kmPart   = "0";
            mIntPart = ("000" + intPart).substring(intPart.length());
        }
        // 7. 去掉小数部分末尾多余的'0'
        int fLen = fracPart.length();
        while (fLen > 0 && fracPart.charAt(fLen - 1) == '0') {
            fLen--;
        }
        // 8. 最后拼字符串：[-]K{kmPart}+{mIntPart}[.{fracPart(0..fLen)}]
        //    预估一下 StringBuilder 大小，避免自动扩容
        int capacity = 
              (negative ? 1 : 0)   // 可能的符号位
            + 1                   // 'K'
            + kmPart.length()
            + 1                   // '+'
            + mIntPart.length()
            + (fLen > 0 ? 1 + fLen : 0); // 小数点 + 小数长度
        StringBuilder sb = new StringBuilder(capacity);
        if (negative) {
            sb.append('-');
        }
        sb.append('K').append(kmPart).append('+').append(mIntPart);
        if (fLen > 0) {
            sb.append('.').append(fracPart, 0, fLen);
        }
        return sb.toString();
    }

    /**
     * 添加多个签名图片，支持横向或竖向排列，支持自动换行。
     * 从给定位置开始，可以选择只显示第一个有效的图片。
     * 使用来自继承的signImageMap的预加载图像数据。
     *
     * @param sheet          要添加图片的表格。
     * @param startRowIndex  起始行索引。
     * @param startColIndex  起始列索引。
     * @param endColIndex    终止列索引。如果等于startColIndex则竖向添加图片，否则横向添加并在超过endColIndex时换行。
     * @param signIdList     要添加的签名ID列表。
     * @param signName       签名名称，当无签名图片时显示。
     * @param cellStyle      显示signName时使用的单元格样式。
     * @param onlyFirstValid 是否只显示第一个有效的图片，true则在找到第一个有效图片后停止。
     * @return 处理完毕后的下一个位置索引（横向模式返回下一列索引，竖向模式返回下一行索引）。
     */
    protected int addSignatureImages(Sheet sheet, int startRowIndex, int startColIndex, int endColIndex,
            List<String> signIdList, String signName, CellStyle cellStyle, boolean onlyFirstValid) {
        boolean isVertical = (startColIndex == endColIndex);
        // 如果是横向模式且起始列不等于终止列，合并单元格
        if (!isVertical) {
            sheet.addMergedRegion(new CellRangeAddress(startRowIndex, startRowIndex, startColIndex, endColIndex));
        }
        if (signIdList == null || signIdList.isEmpty()) {
            // 如果没有签名，显示文字并返回下一个位置索引
            log.info("位置 ({}, {}) 未提供签名ID", startRowIndex, startColIndex);
            // 确保起始行存在
            Row row = sheet.getRow(startRowIndex);
            if (row == null) {
                row = sheet.createRow(startRowIndex);
            }
            createCell(row, startColIndex, signName, cellStyle);
            
            return startRowIndex + 1;
        }

        log.info("在位置 ({}, {}) 开始添加 {} 个签名图片，模式: {}, 终止列: {}", 
                startRowIndex, startColIndex, signIdList.size(), 
                isVertical ? "竖向" : "横向", endColIndex);

        Drawing<?> drawing = sheet.createDrawingPatriarch();
        CreationHelper helper = sheet.getWorkbook().getCreationHelper();
        int currentRowIndex = startRowIndex;
        int currentColIndex = startColIndex;
        boolean foundValidImage = false;

        for (String signId : signIdList) {
            if (signId == null || signId.trim().isEmpty()) {
                log.warn("跳过空或null签名ID");
                continue;
            }

            log.debug("处理签名ID: {} 在位置 ({}, {})", signId, currentRowIndex, currentColIndex);

            // 从继承的映射中获取预加载的图像数据
            byte[] imageData = signImageMap.get(signId);
            if (imageData == null) {
                log.warn("未找到签名ID: {} 的预加载图像数据。跳过图像。", signId);
                // 跳过此ID的图像放置
                continue;
            } else {
                log.debug("使用缓存图像数据: {} 字节，ID: {}", imageData.length, signId);
            }

            // 仅在图像数据有效时继续
            if (imageData.length > 0) {
                try {
                    // 确保目标行存在
                    Row row = sheet.getRow(currentRowIndex);
                    if (row == null) {
                        row = sheet.createRow(currentRowIndex);
                    }
                    // 为签名图像设置适当的行高
                    row.setHeightInPoints(SIGNATURE_HEIGHT_POINTS);

                    // 使用继承的方法确定图片类型
                    int pictureType = determinePictureType(imageData);
                    log.debug("确定图片类型: {} (ID: {})", pictureType, signId);

                    // 向工作簿添加图片数据
                    int pictureIndex = sheet.getWorkbook().addPicture(imageData, pictureType);
                    log.debug("图片已添加到工作簿，索引: {} (ID: {})", pictureIndex, signId);

                    // 创建锚点以在单元格内放置图片
                    ClientAnchor anchor = helper.createClientAnchor();
                    anchor.setCol1(currentColIndex);
                    anchor.setRow1(currentRowIndex);
                    anchor.setCol2(currentColIndex + 1);
                    anchor.setRow2(currentRowIndex + 1);
                    anchor.setAnchorType(ClientAnchor.AnchorType.DONT_MOVE_AND_RESIZE);

                    // 创建图片形状
                    Picture pict = drawing.createPicture(anchor, pictureIndex);

                    log.debug("已为ID: {} 创建图片，锚点位于单元格 ({}, {})", 
                            signId, currentRowIndex, currentColIndex);

                    // 标记找到了有效图片
                    foundValidImage = true;

                    // 移动到下一个位置
                    if (isVertical) {
                        currentRowIndex++;
                    } else {
                        currentColIndex++;
                        // 如果超过终止列索引，换到下一行的起始列
                        if (currentColIndex > endColIndex) {
                            currentRowIndex++;
                            currentColIndex = startColIndex;
                        }
                    }

                    // 如果只需要第一个有效图片且已找到，则停止处理
                    if (onlyFirstValid) {
                        break;
                    }
                } catch (Exception e) {
                    log.error("在位置 ({}, {}) 为ID: {} 添加签名图像时出错", 
                            currentRowIndex, currentColIndex, signId, e);
                    // 移至下一个位置以避免覆盖
                    if (isVertical) {
                        currentRowIndex++;
                    } else {
                        currentColIndex++;
                        // 如果超过终止列索引，换到下一行的起始列
                        if (currentColIndex > endColIndex) {
                            currentRowIndex++;
                            currentColIndex = startColIndex;
                        }
                    }
                }
            } else {
                log.warn("签名ID: {} 的图像数据在检索后为空。跳过图像。", signId);
            }
        }

        // 如果没有找到有效图片，则显示文字
        if (!foundValidImage) {
            Row row = sheet.getRow(startRowIndex);
            if (row == null) {
                row = sheet.createRow(startRowIndex);
            }
            createCell(row, startColIndex, signName, cellStyle);

            // 如果是竖向模式且没找到有效图片，需要更新行索引
            if (isVertical) {
                currentRowIndex = startRowIndex + 1;
            } else {
                currentColIndex = startColIndex + 1;
            }
        }

        log.info("完成添加签名。最后位置: ({}, {})", 
                isVertical ? currentRowIndex - 1 : currentRowIndex, 
                isVertical ? startColIndex : currentColIndex - 1);
        // 返回下一个可用的位置索引
        return currentRowIndex;
    }

    /**
     * 判断给定文本在指定列宽下是否需要换行
     * @param text 文本内容
     * @param columnWidth 列宽（字符数）
     * @return 是否需要换行
     */
    protected boolean needsTextWrapping(String text, int columnWidth) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        // 保守估计，通常中文字符宽度是英文字符的两倍
        int effectiveLength = 0;
        for (char c : text.toCharArray()) {
            if (Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS || 
                c > 0x3000) {
                effectiveLength += 2;  // 中文字符计为2个宽度单位
            } else {
                effectiveLength += 1;  // 英文字符计为1个宽度单位
            }
        }
        // 估计是否超过可显示范围并需要换行
        // 列宽基于字符数，减去一些边距空间
        return effectiveLength > columnWidth || text.contains("\n");
    }

    /**
     * 设置行的条件高度 - 如果任何单元格内容需要换行，使用自动高度；否则使用指定的最小高度
     * 
     * @param row 要设置高度的行
     * @param startCol 起始列索引
     * @param endCol 结束列索引
     * @param columnWidths 每列的宽度（字符数的估计值）数组
     * @param minHeightPoints 最小行高（以点为单位）
     */
    protected void setConditionalRowHeight(Row row, int startCol, int endCol, int[] columnWidths, float minHeightPoints) {
        if (row == null) {
            return;
        }

        boolean needsWrapping = false;
        
        // 检查该行的每个单元格是否需要换行
        for (int i = startCol; i <= endCol; i++) {
            Cell cell = row.getCell(i);
            if (cell != null) {
                String cellValue = cell.toString();
                int columnWidth = (i < columnWidths.length) ? columnWidths[i] : 10; // 默认宽度
                if (needsTextWrapping(cellValue, columnWidth)) {
                    needsWrapping = true;
                    break;
                }
            }
        }
        
        // 根据需要设置行高
        if (!needsWrapping) {
            row.setHeightInPoints(minHeightPoints);
        } else {
            // 计算因换行导致的范围内任何单元格所需的最大高度
            float maxCalculatedHeight = 0f;
            Sheet sheet = row.getSheet();
            Workbook workbook = sheet.getWorkbook();
            // AWT FontRenderContext可以重用
            java.awt.font.FontRenderContext frc = new java.awt.font.FontRenderContext(null, true, true);

            for (int colIdx = startCol; colIdx <= endCol; colIdx++) {
                Cell cell = row.getCell(colIdx);
                if (cell != null && cell.getCellType() != CellType.BLANK) {
                    String cellValue = "";
                    // 安全地获取单元格值作为字符串
                    try {
                        if (cell.getCellType() == CellType.STRING) {
                            cellValue = cell.getStringCellValue();
                        } else {
                            // 对其他类型使用DataFormatter获取字符串表示形式
                            // 对于各种数据类型，这通常比cell.toString()更安全
                            DataFormatter formatter = new DataFormatter();
                            cellValue = formatter.formatCellValue(cell);
                        }
                    } catch (Exception e) {
                        // 如果需要特定处理，则回退或记录日志
                        cellValue = cell.toString(); 
                    }

                    if (cellValue != null && !cellValue.isEmpty()) {
                        CellStyle cellStyle = cell.getCellStyle();
                        org.apache.poi.ss.usermodel.Font poiFont = workbook.getFontAt(cellStyle.getFontIndex());
                        
                        float columnPixelWidth = sheet.getColumnWidthInPixels(colIdx);
                        // 如果columnPixelWidth太小或为零，则回退
                        if (columnPixelWidth <= 1f) { 
                            float estimatedAvgCharPixelWidth = poiFont.getFontHeightInPoints() * 0.6f; // 启发式：平均字符宽度
                            int characterColumnWidth = (columnWidths != null && colIdx < columnWidths.length) ? columnWidths[colIdx] : 10; // 默认字符宽度10
                            columnPixelWidth = characterColumnWidth * estimatedAvgCharPixelWidth;
                            if (columnPixelWidth <= 1f) {
                                columnPixelWidth = 50f; // 最后的默认像素宽度（例如50px）
                            }
                        }

                        // 创建AWT字体以使用TextLayout进行精确的文本度量
                        int awtStyle = java.awt.Font.PLAIN;
                        if (poiFont.getBold()) awtStyle |= java.awt.Font.BOLD;
                        if (poiFont.getItalic()) awtStyle |= java.awt.Font.ITALIC;
                        java.awt.Font awtFont = new java.awt.Font(poiFont.getFontName(), awtStyle, poiFont.getFontHeightInPoints());
                        
                        java.text.AttributedString attributedString = new java.text.AttributedString(cellValue);
                        attributedString.addAttribute(java.awt.font.TextAttribute.FONT, awtFont, 0, cellValue.length());

                        java.awt.font.TextLayout layout = new java.awt.font.TextLayout(attributedString.getIterator(), frc);
                        float textPixelWidth = (float) layout.getBounds().getWidth();
                        
                        int lineCount = 0;
                        if (!cellValue.isEmpty()) {
                            lineCount = 1; // 如果存在文本，则最少1行
                            if (columnPixelWidth > 0) {
                                lineCount = Math.max(lineCount, (int) Math.ceil(textPixelWidth / columnPixelWidth));
                            } else if (textPixelWidth > 0) { // 列没有有效宽度，但文本存在
                                lineCount = Math.max(lineCount, 5); // 启发式：假设约5行
                            }
                            // 计算文本中的显式换行符
                            int explicitNewlines = 0;
                            for (char c : cellValue.toCharArray()) {
                                if (c == '\n') { // 修正linter错误：字符常量应该是'\n'
                                    explicitNewlines++;
                                }
                            }
                            lineCount = Math.max(lineCount, explicitNewlines + 1);
                        }

                        if (lineCount > 0) {
                            // 为了保持一致性，使用setAdaptiveRowHeight中的行高因子1.8f
                            float calculatedCellHeight = lineCount * poiFont.getFontHeightInPoints() * 1.8f; 
                            maxCalculatedHeight = Math.max(maxCalculatedHeight, calculatedCellHeight);
                        }
                    }
                }
            }
            // 将行高设置为计算得出的换行内容高度和指定的minHeightPoints中的最大值
            row.setHeightInPoints(Math.max(maxCalculatedHeight, minHeightPoints));
        }
    }

    /**
     * 设置行的条件高度 - 使用默认最小行高
     * 
     * @param row 要设置高度的行
     * @param startCol 起始列索引
     * @param endCol 结束列索引
     * @param columnWidths 每列的宽度（字符数的估计值）数组
     */
    protected void setConditionalRowHeight(Row row, int startCol, int endCol, int[] columnWidths) {
        setConditionalRowHeight(row, startCol, endCol, columnWidths, DEFAULT_MIN_ROW_HEIGHT);
    }

    /**
     * 计算自适应行高 （有效）
     * @param sheet 表格对象
     * @param cell 要计算的单元格内容
     * @param contentRow 所在行
     * @param minHeight 最小高度
     * @param itemContentStyle 适用样式
     */
    protected void setAdaptiveRowHeight(Sheet sheet, Cell cell, Row contentRow,Float minHeight,CellStyle itemContentStyle) {
        // 1. 获取合并后的列宽总和（单位：字符）
        int mergedColumnWidth = 0;
        for (int col = 1; col <= 6; col++) {
            mergedColumnWidth += sheet.getColumnWidth(col); // 列宽单位：1/256字符
        }
        double totalCharWidth = mergedColumnWidth / 256.0; // 转换为字符数

        // 2. 获取文本内容
        String text = cell.getStringCellValue();
        if (text == null || text.isEmpty()) {
            // 无内容直接设置最小高度
            contentRow.setHeightInPoints(minHeight);
            return;
        }

        // 3. 计算基于换行符的行数
        int newlineCount = text.split("\n").length - 1; // 换行符数量
        int minLineCount = newlineCount + 1; // 至少要有换行符数+1行

        // 4. 计算基于宽度的行数
        Workbook workbook = sheet.getWorkbook();
        Font font = workbook.getFontAt(itemContentStyle.getFontIndex());
        int avgCharWidth = 7; // 假设每个字符宽度约7像素（根据字体调整）
        int lineWidth = (int) (totalCharWidth * avgCharWidth); // 合并区域总像素宽度

        AttributedString attributedText = new AttributedString(text);
        FontRenderContext frc = new FontRenderContext(null, true, true);
        TextLayout layout = new TextLayout(attributedText.getIterator(), frc);
        int textWidth = (int) layout.getBounds().getWidth();
        int widthBasedLineCount = (int) Math.ceil((double) textWidth / lineWidth);

        // 5. 取两种计算方式的最大值
        int totalLineCount = Math.max(minLineCount, widthBasedLineCount);

        // 4. 设置行高（行高 = 行数 × 字体大小 × 行高系数）
        float fontSize = font.getFontHeightInPoints();
        float height = totalLineCount * fontSize * 1.8f;
        contentRow.setHeightInPoints(Math.max(minHeight, height));
    }



}
